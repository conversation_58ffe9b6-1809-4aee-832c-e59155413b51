import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing auth state
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
  });

  test('should display login form correctly', async ({ page }) => {
    await page.goto('/login');

    // Check page title and heading
    await expect(page).toHaveTitle(/WebApp/);
    await expect(page.getByRole('heading', { name: /welcome back/i })).toBeVisible();

    // Check form elements
    await expect(page.getByLabel(/email/i)).toBeVisible();
    await expect(page.getByLabel(/password/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();

    // Check navigation links
    await expect(page.getByRole('link', { name: /sign up/i })).toBeVisible();
    await expect(page.getByRole('link', { name: /forgot password/i })).toBeVisible();
  });

  test('should display signup form correctly', async ({ page }) => {
    await page.goto('/signup');

    // Check page title and heading
    await expect(page).toHaveTitle(/WebApp/);
    await expect(page.getByRole('heading', { name: /create your account/i })).toBeVisible();

    // Check form elements
    await expect(page.getByLabel(/full name/i)).toBeVisible();
    await expect(page.getByLabel(/email/i)).toBeVisible();
    await expect(page.getByLabel(/^password$/i)).toBeVisible();
    await expect(page.getByLabel(/confirm password/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /create account/i })).toBeVisible();

    // Check navigation links
    await expect(page.getByRole('link', { name: /sign in/i })).toBeVisible();
  });

  test('should navigate between login and signup', async ({ page }) => {
    await page.goto('/login');

    // Navigate to signup
    await page.getByRole('link', { name: /sign up/i }).click();
    await expect(page).toHaveURL('/signup');
    await expect(page.getByRole('heading', { name: /create your account/i })).toBeVisible();

    // Navigate back to login
    await page.getByRole('link', { name: /sign in/i }).click();
    await expect(page).toHaveURL('/login');
    await expect(page.getByRole('heading', { name: /welcome back/i })).toBeVisible();
  });

  test('should show validation errors for empty login form', async ({ page }) => {
    await page.goto('/login');

    // Try to submit empty form
    await page.getByRole('button', { name: /sign in/i }).click();

    // Check that required fields are highlighted
    const emailInput = page.getByLabel(/email/i);
    const passwordInput = page.getByLabel(/password/i);
    
    await expect(emailInput).toHaveAttribute('required');
    await expect(passwordInput).toHaveAttribute('required');
  });

  test('should show validation errors for empty signup form', async ({ page }) => {
    await page.goto('/signup');

    // Try to submit empty form
    await page.getByRole('button', { name: /create account/i }).click();

    // Check that required fields are highlighted
    const emailInput = page.getByLabel(/email/i);
    const passwordInput = page.getByLabel(/^password$/i);
    const confirmPasswordInput = page.getByLabel(/confirm password/i);
    
    await expect(emailInput).toHaveAttribute('required');
    await expect(passwordInput).toHaveAttribute('required');
    await expect(confirmPasswordInput).toHaveAttribute('required');
  });

  test('should handle login form submission', async ({ page }) => {
    await page.goto('/login');

    // Fill in the form
    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/password/i).fill('password123');

    // Submit the form
    await page.getByRole('button', { name: /sign in/i }).click();

    // Check that the button shows loading state
    await expect(page.getByText(/signing in/i)).toBeVisible();
    
    // Wait for form submission to complete
    await page.waitForTimeout(1000);
  });

  test('should handle signup form submission', async ({ page }) => {
    await page.goto('/signup');

    // Fill in the form
    await page.getByLabel(/full name/i).fill('Test User');
    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/^password$/i).fill('password123');
    await page.getByLabel(/confirm password/i).fill('password123');

    // Submit the form
    await page.getByRole('button', { name: /create account/i }).click();

    // Check that the button shows loading state
    await expect(page.getByText(/creating account/i)).toBeVisible();
    
    // Wait for form submission to complete
    await page.waitForTimeout(1000);
  });

  test('should redirect unauthenticated users from protected routes', async ({ page }) => {
    // Try to access dashboard without authentication
    await page.goto('/dashboard');

    // Should be redirected to login
    await expect(page).toHaveURL(/\/login/);
    await expect(page.getByRole('heading', { name: /welcome back/i })).toBeVisible();
  });

  test('should preserve redirect URL after login', async ({ page }) => {
    // Try to access dashboard, get redirected to login with redirect param
    await page.goto('/dashboard');
    await expect(page).toHaveURL(/\/login\?redirect=%2Fdashboard/);
  });

  test('should show form validation errors', async ({ page }) => {
    await page.goto('/login');

    // Fill in invalid email
    await page.getByLabel(/email/i).fill('invalid-email');
    await page.getByLabel(/password/i).fill('123');

    // Submit the form
    await page.getByRole('button', { name: /sign in/i }).click();

    // Wait for potential validation errors to appear
    await page.waitForTimeout(500);
  });

  test('should handle responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/login');

    // Check that form is still visible and usable
    await expect(page.getByRole('heading', { name: /welcome back/i })).toBeVisible();
    await expect(page.getByLabel(/email/i)).toBeVisible();
    await expect(page.getByLabel(/password/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();

    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.goto('/signup');

    // Check that signup form is still visible and usable
    await expect(page.getByRole('heading', { name: /create your account/i })).toBeVisible();
    await expect(page.getByLabel(/email/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /create account/i })).toBeVisible();
  });

  test('should have proper accessibility attributes', async ({ page }) => {
    await page.goto('/login');

    // Check form labels are properly associated
    const emailInput = page.getByLabel(/email/i);
    const passwordInput = page.getByLabel(/password/i);

    await expect(emailInput).toHaveAttribute('type', 'email');
    await expect(passwordInput).toHaveAttribute('type', 'password');
    await expect(emailInput).toHaveAttribute('required');
    await expect(passwordInput).toHaveAttribute('required');

    // Check button is properly labeled
    const submitButton = page.getByRole('button', { name: /sign in/i });
    await expect(submitButton).toHaveAttribute('type', 'submit');
  });
});
