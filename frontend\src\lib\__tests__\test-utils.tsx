import React, { ReactElement } from "react";
import { render, RenderOptions } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider } from "@/context/auth-context";
import type { User, AuthTokens } from "@/types";

// Mock user data for testing
export const mockUser: User = {
  id: "1",
  email: "<EMAIL>",
  full_name: "Test User",
  is_active: true,
  is_verified: true,
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
};

export const mockTokens: AuthTokens = {
  access_token: "mock-access-token",
  refresh_token: "mock-refresh-token",
  token_type: "bearer",
};

// Create a custom render function that includes providers
interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
  initialUser?: User | null;
  initialTokens?: AuthTokens | null;
}

function createTestQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
}

export function renderWithProviders(
  ui: ReactElement,
  {
    initialUser = null,
    initialTokens = null,
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  const queryClient = createTestQueryClient();

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>
        <AuthProvider>{children}</AuthProvider>
      </QueryClientProvider>
    );
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Mock API responses
export const mockApiResponses = {
  loginSuccess: {
    access_token: "mock-access-token",
    refresh_token: "mock-refresh-token",
    token_type: "bearer",
  },
  loginError: {
    detail: "Invalid credentials",
  },
  signupSuccess: {
    message: "User created successfully",
    user: mockUser,
  },
  signupError: {
    detail: "Email already exists",
  },
  userProfile: mockUser,
};

// Helper functions for testing
export const waitForLoadingToFinish = () =>
  new Promise((resolve) => setTimeout(resolve, 0));

export const mockFetch = (response: any, status = 200) => {
  return jest.fn().mockResolvedValue({
    ok: status >= 200 && status < 300,
    status,
    json: jest.fn().mockResolvedValue(response),
  });
};

export const mockFetchError = (status = 400, message = "Bad Request") => {
  return jest.fn().mockRejectedValue(new Error(message));
};

// Form testing helpers
export const fillForm = async (
  getByLabelText: any,
  formData: Record<string, string>
) => {
  const { userEvent } = await import("@testing-library/user-event");
  const user = userEvent.setup();

  for (const [label, value] of Object.entries(formData)) {
    const input = getByLabelText(new RegExp(label, "i"));
    await user.clear(input);
    await user.type(input, value);
  }
};

export const submitForm = async (
  getByRole: any,
  buttonText = /submit|sign|login/i
) => {
  const { userEvent } = await import("@testing-library/user-event");
  const user = userEvent.setup();

  const submitButton = getByRole("button", { name: buttonText });
  await user.click(submitButton);
};

// Re-export everything from testing-library
export * from "@testing-library/react";
export { default as userEvent } from "@testing-library/user-event";

// Simple test to prevent "no tests" error
describe("Test Utils", () => {
  it("exports mock data correctly", () => {
    expect(mockUser).toBeDefined();
    expect(mockTokens).toBeDefined();
    expect(mockApiResponses).toBeDefined();
  });
});
