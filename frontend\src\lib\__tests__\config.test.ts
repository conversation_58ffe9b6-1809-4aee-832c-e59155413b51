import { config } from '../config'

describe('Config', () => {
  const originalEnv = process.env

  beforeEach(() => {
    jest.resetModules()
    process.env = { ...originalEnv }
  })

  afterAll(() => {
    process.env = originalEnv
  })

  it('uses environment variables when available', () => {
    process.env.NEXT_PUBLIC_API_BASE_URL = 'https://api.example.com'
    process.env.NEXT_PUBLIC_API_VERSION = 'v2'
    process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME = 'custom_session'
    process.env.NEXT_PUBLIC_TOKEN_STORAGE_KEY = 'custom_tokens'
    process.env.NEXT_PUBLIC_APP_NAME = 'Custom App'

    // Re-import config to get updated values
    jest.resetModules()
    const { config: newConfig } = require('../config')

    expect(newConfig.API_BASE_URL).toBe('https://api.example.com')
    expect(newConfig.API_VERSION).toBe('v2')
    expect(newConfig.AUTH_COOKIE_NAME).toBe('custom_session')
    expect(newConfig.TOKEN_STORAGE_KEY).toBe('custom_tokens')
    expect(newConfig.APP_NAME).toBe('Custom App')
  })

  it('uses default values when environment variables are not set', () => {
    delete process.env.NEXT_PUBLIC_API_BASE_URL
    delete process.env.NEXT_PUBLIC_API_VERSION
    delete process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME
    delete process.env.NEXT_PUBLIC_TOKEN_STORAGE_KEY
    delete process.env.NEXT_PUBLIC_APP_NAME

    // Re-import config to get default values
    jest.resetModules()
    const { config: newConfig } = require('../config')

    expect(newConfig.API_BASE_URL).toBe('http://localhost:8000')
    expect(newConfig.API_VERSION).toBe('v1')
    expect(newConfig.AUTH_COOKIE_NAME).toBe('session_token')
    expect(newConfig.TOKEN_STORAGE_KEY).toBe('auth_tokens')
    expect(newConfig.APP_NAME).toBe('WebApp')
  })

  it('has all required configuration properties', () => {
    expect(config).toHaveProperty('API_BASE_URL')
    expect(config).toHaveProperty('API_VERSION')
    expect(config).toHaveProperty('AUTH_COOKIE_NAME')
    expect(config).toHaveProperty('TOKEN_STORAGE_KEY')
    expect(config).toHaveProperty('APP_NAME')
  })

  it('has correct types for all properties', () => {
    expect(typeof config.API_BASE_URL).toBe('string')
    expect(typeof config.API_VERSION).toBe('string')
    expect(typeof config.AUTH_COOKIE_NAME).toBe('string')
    expect(typeof config.TOKEN_STORAGE_KEY).toBe('string')
    expect(typeof config.APP_NAME).toBe('string')
  })

  it('constructs valid API URLs', () => {
    const fullApiUrl = `${config.API_BASE_URL}/${config.API_VERSION}`
    
    // Should be a valid URL format
    expect(fullApiUrl).toMatch(/^https?:\/\/.+\/v\d+$/)
  })

  it('has non-empty configuration values', () => {
    expect(config.API_BASE_URL).toBeTruthy()
    expect(config.API_VERSION).toBeTruthy()
    expect(config.AUTH_COOKIE_NAME).toBeTruthy()
    expect(config.TOKEN_STORAGE_KEY).toBeTruthy()
    expect(config.APP_NAME).toBeTruthy()
  })
})
