{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nextjs/webapp/frontend/src/app/%28auth%29/layout.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\n\nexport const metadata: Metadata = {\n  title: \"Authentication - WebApp\",\n  description: \"Sign in or create an account to access your dashboard\",\n};\n\ninterface AuthLayoutProps {\n  children: React.ReactNode;\n}\n\nexport default function AuthLayout({ children }: AuthLayoutProps) {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-background p-4\">\n      <div className=\"w-full max-w-md\">\n        {children}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAMe,SAAS,WAAW,EAAE,QAAQ,EAAmB;IAC9D,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT", "debugId": null}}]}