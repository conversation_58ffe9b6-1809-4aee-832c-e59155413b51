self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"60d65ba70388420164b7c69615ac34be0a318c18dc\": {\n      \"workers\": {\n        \"app/(auth)/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(auth)/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/actions/auth-actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(auth)/login/page\": \"action-browser\"\n      }\n    },\n    \"60bbd616165892379a16474ad8c6fde51fe844c9ab\": {\n      \"workers\": {\n        \"app/(auth)/signup/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(auth)/signup/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/actions/auth-actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(auth)/signup/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"SuSYmNzNxdjwQUE+c0nO4M1Kc0A3uvu13EOSwN5mCbg=\"\n}"