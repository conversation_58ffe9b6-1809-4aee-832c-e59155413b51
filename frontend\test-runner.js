#!/usr/bin/env node

/**
 * Test Runner Script
 * 
 * This script helps debug and run tests with better error reporting.
 * Usage: node test-runner.js [test-type] [test-file]
 * 
 * Examples:
 * node test-runner.js unit config.test.ts
 * node test-runner.js e2e auth.spec.ts
 * node test-runner.js all
 */

const { spawn } = require('child_process');
const path = require('path');

const testType = process.argv[2] || 'unit';
const testFile = process.argv[3];

function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`Running: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function runTests() {
  try {
    console.log('🧪 Starting test runner...\n');

    // Check if dependencies are installed
    console.log('📦 Checking dependencies...');
    await runCommand('npm', ['list', 'jest', '@testing-library/react', 'playwright'], { stdio: 'pipe' });
    console.log('✅ Dependencies check passed\n');

    switch (testType) {
      case 'unit':
        console.log('🔬 Running unit tests...');
        const jestArgs = ['test'];
        if (testFile) {
          jestArgs.push('--testPathPatterns', testFile);
        }
        jestArgs.push('--verbose', '--no-cache');
        await runCommand('npm', jestArgs);
        break;

      case 'e2e':
        console.log('🌐 Running E2E tests...');
        const playwrightArgs = ['run', 'test:e2e'];
        if (testFile) {
          playwrightArgs.push('--', testFile);
        }
        await runCommand('npm', playwrightArgs);
        break;

      case 'coverage':
        console.log('📊 Running tests with coverage...');
        await runCommand('npm', ['run', 'test:coverage']);
        break;

      case 'all':
        console.log('🚀 Running all tests...');
        await runCommand('npm', ['run', 'test:all']);
        break;

      case 'debug':
        console.log('🐛 Running tests in debug mode...');
        const debugArgs = ['test', '--detectOpenHandles', '--forceExit', '--verbose'];
        if (testFile) {
          debugArgs.push('--testPathPatterns', testFile);
        }
        await runCommand('npm', debugArgs);
        break;

      default:
        console.log('❌ Unknown test type. Available options:');
        console.log('  unit     - Run unit tests');
        console.log('  e2e      - Run E2E tests');
        console.log('  coverage - Run tests with coverage');
        console.log('  all      - Run all tests');
        console.log('  debug    - Run tests in debug mode');
        process.exit(1);
    }

    console.log('\n✅ Tests completed successfully!');
  } catch (error) {
    console.error('\n❌ Test execution failed:');
    console.error(error.message);
    process.exit(1);
  }
}

// Environment checks
function checkEnvironment() {
  console.log('🔍 Environment check:');
  console.log(`Node.js version: ${process.version}`);
  console.log(`Working directory: ${process.cwd()}`);
  console.log(`Test type: ${testType}`);
  if (testFile) {
    console.log(`Test file: ${testFile}`);
  }
  console.log('');
}

// Main execution
checkEnvironment();
runTests().catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
