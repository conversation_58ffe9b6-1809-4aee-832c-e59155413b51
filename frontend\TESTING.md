# Testing Guide

This document provides comprehensive information about the testing strategy and setup for the Next.js 15 frontend application.

## Testing Stack

- **Unit Testing**: Jest + React Testing Library
- **E2E Testing**: Playwright
- **Test Environment**: jsdom for unit tests, real browsers for E2E

## Test Structure

```
src/
├── components/
│   └── auth/
│       └── __tests__/
│           ├── login-form.test.tsx
│           └── signup-form.test.tsx
├── context/
│   └── __tests__/
│       └── auth-context.test.tsx
├── lib/
│   ├── __tests__/
│   │   ├── api.test.ts
│   │   ├── config.test.ts
│   │   ├── test-utils.tsx
│   │   └── validations.test.ts
│   └── actions/
│       └── __tests__/
│           └── auth-actions.test.ts
e2e/
├── auth.spec.ts
└── dashboard.spec.ts
```

## Available Scripts

```bash
# Unit tests
npm test                    # Run all unit tests
npm run test:watch         # Run tests in watch mode
npm run test:coverage      # Run tests with coverage report

# E2E tests
npm run test:e2e           # Run all E2E tests
npm run test:e2e:ui        # Run E2E tests with UI mode

# All tests
npm run test:all           # Run both unit and E2E tests
```

## Test Configuration

### Jest Configuration (`jest.config.js`)

- Uses Next.js Jest configuration
- jsdom test environment
- Path mapping for `@/` imports
- Coverage thresholds set to 70%
- Excludes E2E tests from unit test runs

### Playwright Configuration (`playwright.config.ts`)

- Tests against Chromium, Firefox, and WebKit
- Mobile device testing (Pixel 5, iPhone 12)
- Automatic dev server startup
- Screenshot and video recording on failure

## Test Utilities

### `src/lib/__tests__/test-utils.tsx`

Provides common testing utilities:

```typescript
// Mock data
export const mockUser: User
export const mockTokens: AuthTokens
export const mockApiResponses

// Custom render with providers
export function renderWithProviders(ui, options)

// Helper functions
export const fillForm
export const submitForm
export const mockFetch
```

## Writing Tests

### Unit Tests

```typescript
import { renderWithProviders } from '@/lib/__tests__/test-utils'
import { MyComponent } from '../my-component'

describe('MyComponent', () => {
  it('renders correctly', () => {
    renderWithProviders(<MyComponent />)
    expect(screen.getByText('Hello')).toBeInTheDocument()
  })
})
```

### E2E Tests

```typescript
import { test, expect } from '@playwright/test'

test('user can login', async ({ page }) => {
  await page.goto('/login')
  await page.fill('[name="email"]', '<EMAIL>')
  await page.fill('[name="password"]', 'password')
  await page.click('button[type="submit"]')
  await expect(page).toHaveURL('/dashboard')
})
```

## Test Coverage

Current test coverage includes:

### Unit Tests
- ✅ Authentication forms (login, signup)
- ✅ Authentication context
- ✅ Server actions
- ✅ API client
- ✅ Configuration
- ✅ Validation schemas

### E2E Tests
- ✅ Authentication flow
- ✅ Form validation
- ✅ Navigation
- ✅ Responsive design
- ✅ Dashboard access control

## Mocking Strategy

### API Calls
- Mock axios for unit tests
- Mock fetch for server actions
- Use Playwright route interception for E2E

### Next.js Features
- Mock `next/navigation` hooks
- Mock `next/headers` for server actions
- Mock localStorage and other browser APIs

### Authentication
- Mock tokens in localStorage
- Mock API responses for user data
- Test both authenticated and unauthenticated states

## Best Practices

1. **Test Behavior, Not Implementation**
   - Focus on user interactions and outcomes
   - Avoid testing internal component state

2. **Use Semantic Queries**
   - Prefer `getByRole`, `getByLabelText` over `getByTestId`
   - Write tests that reflect how users interact with the app

3. **Mock External Dependencies**
   - Mock API calls and external services
   - Use consistent mock data across tests

4. **Test Error States**
   - Test loading states, error handling, and edge cases
   - Ensure graceful degradation

5. **Keep Tests Independent**
   - Each test should be able to run in isolation
   - Clean up state between tests

## Debugging Tests

### Jest
```bash
# Run specific test file
npm test -- login-form.test.tsx

# Run tests in debug mode
npm test -- --detectOpenHandles --forceExit

# Run with verbose output
npm test -- --verbose
```

### Playwright
```bash
# Run with UI mode for debugging
npm run test:e2e:ui

# Run specific test file
npx playwright test auth.spec.ts

# Run with debug mode
npx playwright test --debug
```

## Continuous Integration

Tests are configured to run in CI environments:

- Jest tests run with `--ci` flag
- Playwright tests run in headless mode
- Coverage reports are generated
- Tests must pass before deployment

## Troubleshooting

### Common Issues

1. **Module Resolution Errors**
   - Ensure path mapping is correct in `jest.config.js`
   - Check that all imports use the `@/` alias consistently

2. **Async Test Failures**
   - Use `waitFor` for async operations
   - Ensure proper cleanup in `afterEach` hooks

3. **Mock Issues**
   - Clear mocks between tests with `jest.clearAllMocks()`
   - Ensure mocks are set up before component rendering

4. **E2E Test Flakiness**
   - Use proper wait conditions
   - Avoid hard-coded timeouts
   - Ensure test data isolation

## Future Enhancements

- [ ] Visual regression testing with Playwright
- [ ] Performance testing with Lighthouse CI
- [ ] Accessibility testing with axe-core
- [ ] API contract testing
- [ ] Snapshot testing for components
