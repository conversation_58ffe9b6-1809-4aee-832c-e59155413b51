import React from "react";
import { render, screen, waitFor, act } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider, useAuth } from "../auth-context";
import { mockUser, mockTokens } from "@/lib/__tests__/test-utils";

// Mock the API client
jest.mock("@/lib/api-client", () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    defaults: {
      headers: {
        common: {},
      },
    },
  },
}));

// Test component to access auth context
function TestComponent() {
  const { user, isLoading, isAuthenticated, login, logout } = useAuth();

  return (
    <div>
      <div data-testid="loading">{isLoading ? "loading" : "not-loading"}</div>
      <div data-testid="authenticated">
        {isAuthenticated ? "authenticated" : "not-authenticated"}
      </div>
      <div data-testid="user">{user ? user.email : "no-user"}</div>
      <button onClick={() => login(mockTokens)}>Login</button>
      <button onClick={logout}>Logout</button>
    </div>
  );
}

function renderWithAuth() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false, gcTime: 0 },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    </QueryClientProvider>
  );
}

describe("AuthContext", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();

    // Mock successful user profile fetch
    const { apiClient } = require("@/lib/api-client");
    apiClient.get.mockResolvedValue({ data: mockUser });
  });

  it("provides initial unauthenticated state", () => {
    renderWithAuth();

    expect(screen.getByTestId("loading")).toHaveTextContent("not-loading");
    expect(screen.getByTestId("authenticated")).toHaveTextContent(
      "not-authenticated"
    );
    expect(screen.getByTestId("user")).toHaveTextContent("no-user");
  });

  it("loads user from localStorage on mount", async () => {
    // Set up localStorage with tokens
    localStorage.setItem("auth_tokens", JSON.stringify(mockTokens));

    renderWithAuth();

    await waitFor(() => {
      expect(screen.getByTestId("authenticated")).toHaveTextContent(
        "authenticated"
      );
      expect(screen.getByTestId("user")).toHaveTextContent(mockUser.email);
    });
  });

  it("handles login correctly", async () => {
    renderWithAuth();

    const loginButton = screen.getByText("Login");

    await act(async () => {
      loginButton.click();
    });

    await waitFor(() => {
      expect(screen.getByTestId("authenticated")).toHaveTextContent(
        "authenticated"
      );
      expect(localStorage.getItem("auth_tokens")).toBe(
        JSON.stringify(mockTokens)
      );
    });
  });

  it("handles logout correctly", async () => {
    // Start with authenticated state
    localStorage.setItem("auth_tokens", JSON.stringify(mockTokens));

    renderWithAuth();

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByTestId("authenticated")).toHaveTextContent(
        "authenticated"
      );
    });

    const logoutButton = screen.getByText("Logout");

    await act(async () => {
      logoutButton.click();
    });

    await waitFor(() => {
      expect(screen.getByTestId("authenticated")).toHaveTextContent(
        "not-authenticated"
      );
      expect(screen.getByTestId("user")).toHaveTextContent("no-user");
      expect(localStorage.getItem("auth_tokens")).toBeNull();
    });
  });

  it("handles API errors gracefully", async () => {
    const { apiClient } = require("@/lib/api-client");
    apiClient.get.mockRejectedValue(new Error("API Error"));

    localStorage.setItem("auth_tokens", JSON.stringify(mockTokens));

    renderWithAuth();

    await waitFor(() => {
      // Should clear tokens and reset state on API error
      expect(screen.getByTestId("authenticated")).toHaveTextContent(
        "not-authenticated"
      );
      expect(localStorage.getItem("auth_tokens")).toBeNull();
    });
  });

  it("sets authorization header when tokens are available", async () => {
    const { apiClient } = require("@/lib/api-client");

    renderWithAuth();

    const loginButton = screen.getByText("Login");

    await act(async () => {
      loginButton.click();
    });

    await waitFor(() => {
      expect(apiClient.defaults.headers.common["Authorization"]).toBe(
        `Bearer ${mockTokens.access_token}`
      );
    });
  });

  it("clears authorization header on logout", async () => {
    const { apiClient } = require("@/lib/api-client");

    // Start with authenticated state
    localStorage.setItem("auth_tokens", JSON.stringify(mockTokens));

    renderWithAuth();

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByTestId("authenticated")).toHaveTextContent(
        "authenticated"
      );
    });

    const logoutButton = screen.getByText("Logout");

    await act(async () => {
      logoutButton.click();
    });

    await waitFor(() => {
      expect(
        apiClient.defaults.headers.common["Authorization"]
      ).toBeUndefined();
    });
  });
});
