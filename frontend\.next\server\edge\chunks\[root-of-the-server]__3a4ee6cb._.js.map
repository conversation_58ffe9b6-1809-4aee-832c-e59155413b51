{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/config.ts"], "sourcesContent": ["// Centralized configuration management\nimport { z } from \"zod\";\n\n// Environment configuration schema\nconst configSchema = z.object({\n  // API Configuration\n  API_BASE_URL: z.string().url().default(\"http://localhost:8000\"),\n  API_VERSION: z.string().default(\"v1\"),\n  \n  // Authentication\n  AUTH_COOKIE_NAME: z.string().default(\"session_token\"),\n  TOKEN_STORAGE_KEY: z.string().default(\"auth_tokens\"),\n  \n  // Application\n  APP_NAME: z.string().default(\"WebApp\"),\n  APP_DESCRIPTION: z.string().default(\"A modern web application\"),\n  \n  // Environment\n  NODE_ENV: z.enum([\"development\", \"production\", \"test\"]).default(\"development\"),\n  \n  // Features\n  ENABLE_EMAIL_VERIFICATION: z.boolean().default(true),\n  ENABLE_SOCIAL_AUTH: z.boolean().default(false),\n});\n\n// Parse and validate environment variables\nconst parseConfig = () => {\n  const env = {\n    API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,\n    API_VERSION: process.env.NEXT_PUBLIC_API_VERSION,\n    AUTH_COOKIE_NAME: process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME,\n    TOKEN_STORAGE_KEY: process.env.NEXT_PUBLIC_TOKEN_STORAGE_KEY,\n    APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,\n    APP_DESCRIPTION: process.env.NEXT_PUBLIC_APP_DESCRIPTION,\n    NODE_ENV: process.env.NODE_ENV,\n    ENABLE_EMAIL_VERIFICATION: process.env.NEXT_PUBLIC_ENABLE_EMAIL_VERIFICATION === \"true\",\n    ENABLE_SOCIAL_AUTH: process.env.NEXT_PUBLIC_ENABLE_SOCIAL_AUTH === \"true\",\n  };\n\n  try {\n    return configSchema.parse(env);\n  } catch (error) {\n    console.error(\"Invalid configuration:\", error);\n    throw new Error(\"Configuration validation failed\");\n  }\n};\n\n// Export validated configuration\nexport const config = parseConfig();\n\n// API endpoints builder\nexport const apiEndpoints = {\n  auth: {\n    login: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/login`,\n    register: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/register`,\n    logout: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/logout`,\n    refresh: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/refresh`,\n    verify: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/verify`,\n    resendVerification: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/resend-verification`,\n    me: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/me`,\n  },\n  users: {\n    profile: `${config.API_BASE_URL}/api/${config.API_VERSION}/users/me`,\n    update: `${config.API_BASE_URL}/api/${config.API_VERSION}/users/me`,\n  },\n} as const;\n\n// Type for configuration\nexport type Config = z.infer<typeof configSchema>;\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;AACvC;;AAEA,mCAAmC;AACnC,MAAM,eAAe,qLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,oBAAoB;IACpB,cAAc,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;IACvC,aAAa,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAEhC,iBAAiB;IACjB,kBAAkB,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IACrC,mBAAmB,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAEtC,cAAc;IACd,UAAU,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC7B,iBAAiB,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAEpC,cAAc;IACd,UAAU,qLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAEhE,WAAW;IACX,2BAA2B,qLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/C,oBAAoB,qLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1C;AAEA,2CAA2C;AAC3C,MAAM,cAAc;IAClB,MAAM,MAAM;QACV,YAAY;QACZ,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QACjB,QAAQ;QACR,eAAe;QACf,QAAQ;QACR,2BAA2B,6CAAsD;QACjF,oBAAoB,8CAA+C;IACrE;IAEA,IAAI;QACF,OAAO,aAAa,KAAK,CAAC;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,MAAM,SAAS;AAGf,MAAM,eAAe;IAC1B,MAAM;QACJ,OAAO,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,WAAW,CAAC;QACpE,UAAU,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,cAAc,CAAC;QAC1E,QAAQ,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,YAAY,CAAC;QACtE,SAAS,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,aAAa,CAAC;QACxE,QAAQ,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,YAAY,CAAC;QACtE,oBAAoB,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,yBAAyB,CAAC;QAC/F,IAAI,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,QAAQ,CAAC;IAChE;IACA,OAAO;QACL,SAAS,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,SAAS,CAAC;QACpE,QAAQ,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,SAAS,CAAC;IACrE;AACF"}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\nimport type { NextRequest } from \"next/server\";\nimport { config as appConfig } from \"@/lib/config\";\n\n// Define protected and public routes\nconst protectedRoutes = [\"/dashboard\"];\nconst authRoutes = [\"/login\", \"/signup\", \"/verify\"];\nconst publicRoutes = [\"/\", \"/about\", \"/contact\"];\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Check if user has session token (cookie-based auth)\n  const sessionToken = request.cookies.get(appConfig.AUTH_COOKIE_NAME);\n\n  // Check if user has JWT token in localStorage (will be handled client-side)\n  // For middleware, we primarily rely on session cookies for SSR compatibility\n  const isAuthenticated = !!sessionToken;\n\n  // Handle protected routes\n  if (protectedRoutes.some(route => pathname.startsWith(route))) {\n    if (!isAuthenticated) {\n      const loginUrl = new URL(\"/login\", request.url);\n      loginUrl.searchParams.set(\"redirect\", pathname);\n      return NextResponse.redirect(loginUrl);\n    }\n  }\n\n  // Handle auth routes (redirect authenticated users away)\n  if (authRoutes.some(route => pathname.startsWith(route))) {\n    if (isAuthenticated) {\n      const redirectUrl = request.nextUrl.searchParams.get(\"redirect\") || \"/dashboard\";\n      return NextResponse.redirect(new URL(redirectUrl, request.url));\n    }\n  }\n\n  // Add security headers\n  const response = NextResponse.next();\n\n  // Security headers\n  response.headers.set(\"X-Frame-Options\", \"DENY\");\n  response.headers.set(\"X-Content-Type-Options\", \"nosniff\");\n  response.headers.set(\"Referrer-Policy\", \"strict-origin-when-cross-origin\");\n  response.headers.set(\n    \"Permissions-Policy\",\n    \"camera=(), microphone=(), geolocation=()\"\n  );\n\n  return response;\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     */\n    \"/((?!api|_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)\",\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;;;AAEA,qCAAqC;AACrC,MAAM,kBAAkB;IAAC;CAAa;AACtC,MAAM,aAAa;IAAC;IAAU;IAAW;CAAU;AACnD,MAAM,eAAe;IAAC;IAAK;IAAU;CAAW;AAEzC,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,sDAAsD;IACtD,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC,4HAAA,CAAA,SAAS,CAAC,gBAAgB;IAEnE,4EAA4E;IAC5E,6EAA6E;IAC7E,MAAM,kBAAkB,CAAC,CAAC;IAE1B,0BAA0B;IAC1B,IAAI,gBAAgB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC,SAAS;QAC7D,IAAI,CAAC,iBAAiB;YACpB,MAAM,WAAW,IAAI,IAAI,UAAU,QAAQ,GAAG;YAC9C,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;YACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IACF;IAEA,yDAAyD;IACzD,IAAI,WAAW,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC,SAAS;QACxD,IAAI,iBAAiB;YACnB,MAAM,cAAc,QAAQ,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe;YACpE,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,aAAa,QAAQ,GAAG;QAC/D;IACF;IAEA,uBAAuB;IACvB,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAElC,mBAAmB;IACnB,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAClB,sBACA;IAGF,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}