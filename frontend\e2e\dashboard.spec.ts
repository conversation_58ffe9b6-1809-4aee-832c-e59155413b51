import { test, expect } from '@playwright/test';

// Helper function to mock authentication
async function mockAuthentication(page: any) {
  // Mock localStorage with auth tokens
  await page.addInitScript(() => {
    const mockTokens = {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      token_type: 'bearer',
    };
    localStorage.setItem('auth_tokens', JSON.stringify(mockTokens));
  });

  // Mock API responses
  await page.route('**/api/v1/auth/me', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        id: '1',
        email: '<EMAIL>',
        full_name: 'Test User',
        is_active: true,
        is_verified: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      }),
    });
  });
}

test.describe('Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing auth state
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
  });

  test('should redirect to login when not authenticated', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Should be redirected to login
    await expect(page).toHaveURL(/\/login/);
    await expect(page.getByRole('heading', { name: /welcome back/i })).toBeVisible();
  });

  test('should display dashboard when authenticated', async ({ page }) => {
    await mockAuthentication(page);
    await page.goto('/dashboard');

    // Should stay on dashboard
    await expect(page).toHaveURL('/dashboard');
    
    // Check for dashboard elements
    await expect(page.getByText(/dashboard/i)).toBeVisible();
  });

  test('should display user information in sidebar', async ({ page }) => {
    await mockAuthentication(page);
    await page.goto('/dashboard');

    // Wait for user data to load
    await page.waitForTimeout(1000);

    // Check if user information is displayed
    // Note: This depends on the actual sidebar implementation
    await expect(page.getByText(/<EMAIL>/i)).toBeVisible();
  });

  test('should handle logout functionality', async ({ page }) => {
    await mockAuthentication(page);
    await page.goto('/dashboard');

    // Wait for dashboard to load
    await page.waitForTimeout(1000);

    // Look for logout button/link (this depends on your UI implementation)
    const logoutButton = page.getByRole('button', { name: /logout/i }).or(
      page.getByRole('menuitem', { name: /logout/i })
    );

    if (await logoutButton.isVisible()) {
      await logoutButton.click();

      // Should be redirected to login after logout
      await expect(page).toHaveURL(/\/login/);
      await expect(page.getByRole('heading', { name: /welcome back/i })).toBeVisible();
    }
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    await mockAuthentication(page);
    
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/dashboard');

    // Wait for dashboard to load
    await page.waitForTimeout(1000);

    // Check that dashboard is still accessible on mobile
    await expect(page.getByText(/dashboard/i)).toBeVisible();
  });

  test('should handle navigation within dashboard', async ({ page }) => {
    await mockAuthentication(page);
    await page.goto('/dashboard');

    // Wait for dashboard to load
    await page.waitForTimeout(1000);

    // Test navigation (this depends on your actual dashboard structure)
    // For now, just verify we can stay on the dashboard
    await expect(page).toHaveURL('/dashboard');
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Mock authentication but make API calls fail
    await page.addInitScript(() => {
      const mockTokens = {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        token_type: 'bearer',
      };
      localStorage.setItem('auth_tokens', JSON.stringify(mockTokens));
    });

    // Mock API to return error
    await page.route('**/api/v1/auth/me', async (route) => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ detail: 'Unauthorized' }),
      });
    });

    await page.goto('/dashboard');

    // Should be redirected to login due to API error
    await expect(page).toHaveURL(/\/login/);
  });

  test('should maintain authentication state across page refreshes', async ({ page }) => {
    await mockAuthentication(page);
    await page.goto('/dashboard');

    // Wait for dashboard to load
    await page.waitForTimeout(1000);
    await expect(page).toHaveURL('/dashboard');

    // Refresh the page
    await page.reload();

    // Should still be on dashboard after refresh
    await page.waitForTimeout(1000);
    await expect(page).toHaveURL('/dashboard');
  });

  test('should handle loading states properly', async ({ page }) => {
    await mockAuthentication(page);

    // Add delay to API response to test loading state
    await page.route('**/api/v1/auth/me', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '1',
          email: '<EMAIL>',
          full_name: 'Test User',
          is_active: true,
          is_verified: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        }),
      });
    });

    await page.goto('/dashboard');

    // Should show loading state initially
    // Note: This depends on your loading implementation
    await page.waitForTimeout(500);
    
    // Eventually should show dashboard content
    await page.waitForTimeout(1500);
    await expect(page).toHaveURL('/dashboard');
  });
});
