# WebApp Frontend

A modern Next.js 15 application with comprehensive authentication and dashboard features.

## 🚀 Features

### ✅ Authentication System

- **Login/Signup Forms** with `useActionState` and server actions
- **Form Validation** using Zod schemas with real-time error display
- **Dual Token Strategy** (JWT + HTTP-only cookies) for optimal security
- **Loading States** with spinners and disabled form controls
- **Auto-redirect** after successful authentication
- **Logout Functionality** with proper session cleanup

### ✅ Dashboard & Navigation

- **Responsive Sidebar** with collapsible icon mode
- **User Profile Integration** showing authenticated user data
- **Dynamic Navigation** with authentication-aware components
- **Team/Project Switcher** with configurable branding

### ✅ UI Components

- **shadcn/ui** component library with Tailwind CSS
- **Alert System** for user feedback (success/error messages)
- **Form Controls** with proper accessibility and validation states
- **Responsive Design** optimized for desktop and mobile

### ✅ Architecture & Best Practices

- **Next.js 15** with App Router and React 19
- **Route Groups** for logical organization (`(auth)`, `(dashboard)`)
- **Middleware** for authentication checks and route protection
- **TypeScript** with strict typing throughout
- **Centralized Configuration** with environment-specific settings

### ✅ Testing Strategy

- **Unit Testing** with Jest and React Testing Library
- **E2E Testing** with Playwright across multiple browsers
- **Component Testing** for all authentication forms and UI components
- **Integration Testing** for complete user flows
- **Coverage Reporting** with 70% threshold across all metrics
- **Mock Strategy** for external dependencies and API calls

## 🛠 Tech Stack

- **Framework**: Next.js 15.4.5 with App Router
- **React**: 19.1.0 with modern hooks (`useActionState`)
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: React Query (TanStack Query) + React Context
- **Validation**: Zod schemas for forms and API responses
- **HTTP Client**: Axios with interceptors for token management
- **Icons**: Lucide React
- **Notifications**: Sonner for toast messages
- **Testing**: Jest + React Testing Library + Playwright

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   │   ├── login/         # Login page
│   │   └── signup/        # Signup page
│   ├── (dashboard)/       # Protected dashboard routes
│   │   └── dashboard/     # Main dashboard
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx          # Home page
├── components/            # Reusable components
│   ├── auth/             # Authentication forms
│   ├── dashboard/        # Dashboard-specific components
│   └── ui/               # shadcn/ui components
├── context/              # React contexts
│   └── auth-context.tsx  # Authentication state management
├── lib/                  # Utilities and configurations
│   ├── __tests__/        # Unit tests and test utilities
│   ├── actions/          # Server actions
│   ├── api-client.ts     # API client setup
│   ├── config.ts         # Centralized configuration
│   ├── utils.ts          # Utility functions
│   └── validations.ts    # Zod schemas
├── middleware.ts         # Route protection middleware
├── types/               # TypeScript type definitions
└── __tests__/           # Component and integration tests
e2e/                     # End-to-end tests with Playwright
├── auth.spec.ts         # Authentication flow tests
└── dashboard.spec.ts    # Dashboard functionality tests
```

## 🔧 Configuration

Environment variables in `.env.local`:

```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_API_VERSION=v1

# Authentication
NEXT_PUBLIC_AUTH_COOKIE_NAME=session_token
NEXT_PUBLIC_TOKEN_STORAGE_KEY=auth_tokens

# Application
NEXT_PUBLIC_APP_NAME=WebApp
NEXT_PUBLIC_APP_DESCRIPTION=A modern web application

# Features
NEXT_PUBLIC_ENABLE_EMAIL_VERIFICATION=true
NEXT_PUBLIC_ENABLE_SOCIAL_AUTH=false
```

## 🚦 Getting Started

1. **Install dependencies**:

   ```bash
   npm install
   ```

2. **Set up environment**:

   ```bash
   cp .env.local.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Run development server**:

   ```bash
   npm run dev
   ```

4. **Open browser**: Navigate to [http://localhost:3000](http://localhost:3000)

## 🔐 Authentication Flow

1. **Login/Signup**: Forms use server actions with `useActionState`
2. **Token Storage**: Dual strategy (localStorage + HTTP-only cookies)
3. **Route Protection**: Middleware checks authentication status
4. **Context Management**: React Context provides user state globally
5. **Auto-refresh**: Axios interceptors handle token refresh

## 📋 Available Scripts

### Development

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Testing

- `npm test` - Run unit tests with Jest
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run test:e2e` - Run E2E tests with Playwright
- `npm run test:e2e:ui` - Run E2E tests with Playwright UI
- `npm run test:all` - Run both unit and E2E tests

## 🎯 Current Status

✅ **Authentication System** - Fully functional login/signup with validation
✅ **Dashboard Integration** - User data display and navigation
✅ **Route Protection** - Middleware-based authentication guards
✅ **UI Components** - Complete shadcn/ui integration
✅ **State Management** - React Query + Context setup
✅ **Testing Strategy** - Comprehensive unit and E2E test coverage

## 🧪 Testing

### Test Coverage

- **Unit Tests**: Authentication forms, context, server actions, API client, validations
- **Integration Tests**: Complete authentication flow and user interactions
- **E2E Tests**: Full user journeys across multiple browsers and devices
- **Coverage Threshold**: 70% minimum across branches, functions, lines, and statements

### Test Structure

```
src/
├── components/auth/__tests__/     # Form component tests
├── context/__tests__/             # Context and state management tests
├── lib/__tests__/                 # Utility and API tests
└── lib/actions/__tests__/         # Server action tests
e2e/                               # End-to-end test suites
├── auth.spec.ts                   # Authentication flow tests
└── dashboard.spec.ts              # Dashboard functionality tests
```

### Running Tests

```bash
# Quick test run
npm test

# Watch mode for development
npm run test:watch

# Full test suite with coverage
npm run test:coverage

# E2E tests (requires dev server running)
npm run test:e2e

# All tests
npm run test:all
```

For detailed testing information, see [TESTING.md](./TESTING.md).

## 🔄 Integration

This frontend is designed to work with a **FastAPI backend** providing:

- JWT-based authentication endpoints
- User management APIs
- RESTful API structure with proper error handling

The application follows Next.js 15 best practices and is ready for production deployment.
