(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__3a4ee6cb._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/lib/config.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Centralized configuration management
__turbopack_context__.s({
    "apiEndpoints": ()=>apiEndpoints,
    "config": ()=>config
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [middleware-edge] (ecmascript) <export * as z>");
;
// Environment configuration schema
const configSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    // API Configuration
    API_BASE_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url().default("http://localhost:8000"),
    API_VERSION: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().default("v1"),
    // Authentication
    AUTH_COOKIE_NAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().default("session_token"),
    TOKEN_STORAGE_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().default("auth_tokens"),
    // Application
    APP_NAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().default("WebApp"),
    APP_DESCRIPTION: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().default("A modern web application"),
    // Environment
    NODE_ENV: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "development",
        "production",
        "test"
    ]).default("development"),
    // Features
    ENABLE_EMAIL_VERIFICATION: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true),
    ENABLE_SOCIAL_AUTH: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(false)
});
// Parse and validate environment variables
const parseConfig = ()=>{
    const env = {
        API_BASE_URL: ("TURBOPACK compile-time value", "http://localhost:8000"),
        API_VERSION: ("TURBOPACK compile-time value", "v1"),
        AUTH_COOKIE_NAME: ("TURBOPACK compile-time value", "session_token"),
        TOKEN_STORAGE_KEY: ("TURBOPACK compile-time value", "auth_tokens"),
        APP_NAME: ("TURBOPACK compile-time value", "WebApp"),
        APP_DESCRIPTION: ("TURBOPACK compile-time value", "A modern web application built with Next.js 15 and FastAPI"),
        NODE_ENV: ("TURBOPACK compile-time value", "development"),
        ENABLE_EMAIL_VERIFICATION: ("TURBOPACK compile-time value", "true") === "true",
        ENABLE_SOCIAL_AUTH: ("TURBOPACK compile-time value", "false") === "true"
    };
    try {
        return configSchema.parse(env);
    } catch (error) {
        console.error("Invalid configuration:", error);
        throw new Error("Configuration validation failed");
    }
};
const config = parseConfig();
const apiEndpoints = {
    auth: {
        login: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/login`,
        register: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/register`,
        logout: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/logout`,
        refresh: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/refresh`,
        verify: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/verify`,
        resendVerification: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/resend-verification`,
        me: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/me`
    },
    users: {
        profile: `${config.API_BASE_URL}/api/${config.API_VERSION}/users/me`,
        update: `${config.API_BASE_URL}/api/${config.API_VERSION}/users/me`
    }
};
}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "config": ()=>config,
    "middleware": ()=>middleware
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config.ts [middleware-edge] (ecmascript)");
;
;
// Define protected and public routes
const protectedRoutes = [
    "/dashboard"
];
const authRoutes = [
    "/login",
    "/signup",
    "/verify"
];
const publicRoutes = [
    "/",
    "/about",
    "/contact"
];
function middleware(request) {
    const { pathname } = request.nextUrl;
    // Check if user has session token (cookie-based auth)
    const sessionToken = request.cookies.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["config"].AUTH_COOKIE_NAME);
    // Check if user has JWT token in localStorage (will be handled client-side)
    // For middleware, we primarily rely on session cookies for SSR compatibility
    const isAuthenticated = !!sessionToken;
    // Handle protected routes
    if (protectedRoutes.some((route)=>pathname.startsWith(route))) {
        if (!isAuthenticated) {
            const loginUrl = new URL("/login", request.url);
            loginUrl.searchParams.set("redirect", pathname);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
        }
    }
    // Handle auth routes (redirect authenticated users away)
    if (authRoutes.some((route)=>pathname.startsWith(route))) {
        if (isAuthenticated) {
            const redirectUrl = request.nextUrl.searchParams.get("redirect") || "/dashboard";
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(redirectUrl, request.url));
        }
    }
    // Add security headers
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    // Security headers
    response.headers.set("X-Frame-Options", "DENY");
    response.headers.set("X-Content-Type-Options", "nosniff");
    response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
    response.headers.set("Permissions-Policy", "camera=(), microphone=(), geolocation=()");
    return response;
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */ "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"
    ]
};
}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__3a4ee6cb._.js.map