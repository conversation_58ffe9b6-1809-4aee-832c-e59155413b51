{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/node_modules_zod_v4_e8b14092._.js", "server/edge/chunks/[root-of-the-server]__3a4ee6cb._.js", "server/edge/chunks/edge-wrapper_70516565.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SuSYmNzNxdjwQUE+c0nO4M1Kc0A3uvu13EOSwN5mCbg=", "__NEXT_PREVIEW_MODE_ID": "e7e0d161a17354de103ed1283ba2ef52", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ad8c7cb514bfabd27d43e7e6993d125262563896100fcf70e06146e3ff285072", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3735ee99e3d4d4eea5b062ea30baa8d9e5d74bb1390a4405ca12a23e14fda66a"}}}, "instrumentation": null, "functions": {}}