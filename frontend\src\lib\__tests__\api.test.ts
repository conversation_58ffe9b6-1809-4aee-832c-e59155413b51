import { apiClient } from '../api-client'
import { config } from '../config'

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    defaults: {
      headers: {
        common: {},
      },
    },
    interceptors: {
      request: {
        use: jest.fn(),
      },
      response: {
        use: jest.fn(),
      },
    },
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  })),
}))

describe('API Client', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('creates axios instance with correct base URL', () => {
    const axios = require('axios')

    expect(axios.create).toHaveBeenCalledWith({
      baseURL: `${config.API_BASE_URL}/${config.API_VERSION}`,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  })

  it('sets up request interceptor', () => {
    expect(apiClient.interceptors.request.use).toHaveBeenCalled()
  })

  it('sets up response interceptor', () => {
    expect(apiClient.interceptors.response.use).toHaveBeenCalled()
  })

  it('has correct default headers', () => {
    expect(apiClient.defaults.headers.common).toBeDefined()
  })

  describe('Request Interceptor', () => {
    it('adds authorization header when token is available', () => {
      // Mock localStorage
      const mockTokens = {
        access_token: 'test-token',
        refresh_token: 'refresh-token',
        token_type: 'bearer',
      }

      localStorage.setItem('auth_tokens', JSON.stringify(mockTokens))

      // Get the request interceptor function
      const requestInterceptor = (apiClient.interceptors.request.use as jest.Mock).mock.calls[0][0]

      const config = {
        headers: {},
      }

      const result = requestInterceptor(config)

      expect(result.headers.Authorization).toBe('Bearer test-token')
    })

    it('does not add authorization header when no token is available', () => {
      localStorage.removeItem('auth_tokens')

      // Get the request interceptor function
      const requestInterceptor = (apiClient.interceptors.request.use as jest.Mock).mock.calls[0][0]

      const config = {
        headers: {},
      }

      const result = requestInterceptor(config)

      expect(result.headers.Authorization).toBeUndefined()
    })

    it('handles invalid token data gracefully', () => {
      localStorage.setItem('auth_tokens', 'invalid-json')

      // Get the request interceptor function
      const requestInterceptor = (apiClient.interceptors.request.use as jest.Mock).mock.calls[0][0]

      const config = {
        headers: {},
      }

      const result = requestInterceptor(config)

      expect(result.headers.Authorization).toBeUndefined()
    })
  })

  describe('Response Interceptor', () => {
    it('passes through successful responses', () => {
      // Get the response interceptor success function
      const responseInterceptor = (apiClient.interceptors.response.use as jest.Mock).mock.calls[0][0]

      const response = {
        data: { message: 'success' },
        status: 200,
      }

      const result = responseInterceptor(response)

      expect(result).toBe(response)
    })

    it('handles 401 errors by clearing tokens', () => {
      localStorage.setItem('auth_tokens', JSON.stringify({ access_token: 'token' }))

      // Get the response interceptor error function
      const errorInterceptor = (apiClient.interceptors.response.use as jest.Mock).mock.calls[0][1]

      const error = {
        response: {
          status: 401,
          data: { detail: 'Unauthorized' },
        },
      }

      expect(() => errorInterceptor(error)).toThrow()
      expect(localStorage.getItem('auth_tokens')).toBeNull()
    })

    it('handles 403 errors by clearing tokens', () => {
      localStorage.setItem('auth_tokens', JSON.stringify({ access_token: 'token' }))

      // Get the response interceptor error function
      const errorInterceptor = (apiClient.interceptors.response.use as jest.Mock).mock.calls[0][1]

      const error = {
        response: {
          status: 403,
          data: { detail: 'Forbidden' },
        },
      }

      expect(() => errorInterceptor(error)).toThrow()
      expect(localStorage.getItem('auth_tokens')).toBeNull()
    })

    it('passes through other errors without clearing tokens', () => {
      localStorage.setItem('auth_tokens', JSON.stringify({ access_token: 'token' }))

      // Get the response interceptor error function
      const errorInterceptor = (apiClient.interceptors.response.use as jest.Mock).mock.calls[0][1]

      const error = {
        response: {
          status: 500,
          data: { detail: 'Internal Server Error' },
        },
      }

      expect(() => errorInterceptor(error)).toThrow()
      expect(localStorage.getItem('auth_tokens')).toBe(JSON.stringify({ access_token: 'token' }))
    })

    it('handles network errors', () => {
      // Get the response interceptor error function
      const errorInterceptor = (apiClient.interceptors.response.use as jest.Mock).mock.calls[0][1]

      const error = {
        message: 'Network Error',
        code: 'NETWORK_ERROR',
      }

      expect(() => errorInterceptor(error)).toThrow()
    })
  })
})
