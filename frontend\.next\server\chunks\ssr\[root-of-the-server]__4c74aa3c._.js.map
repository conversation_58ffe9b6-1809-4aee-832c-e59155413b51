{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nextjs/webapp/frontend/src/lib/config.ts"], "sourcesContent": ["// Centralized configuration management\nimport { z } from \"zod\";\n\n// Environment configuration schema\nconst configSchema = z.object({\n  // API Configuration\n  API_BASE_URL: z.string().url().default(\"http://localhost:8000\"),\n  API_VERSION: z.string().default(\"v1\"),\n  \n  // Authentication\n  AUTH_COOKIE_NAME: z.string().default(\"session_token\"),\n  TOKEN_STORAGE_KEY: z.string().default(\"auth_tokens\"),\n  \n  // Application\n  APP_NAME: z.string().default(\"WebApp\"),\n  APP_DESCRIPTION: z.string().default(\"A modern web application\"),\n  \n  // Environment\n  NODE_ENV: z.enum([\"development\", \"production\", \"test\"]).default(\"development\"),\n  \n  // Features\n  ENABLE_EMAIL_VERIFICATION: z.boolean().default(true),\n  ENABLE_SOCIAL_AUTH: z.boolean().default(false),\n});\n\n// Parse and validate environment variables\nconst parseConfig = () => {\n  const env = {\n    API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,\n    API_VERSION: process.env.NEXT_PUBLIC_API_VERSION,\n    AUTH_COOKIE_NAME: process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME,\n    TOKEN_STORAGE_KEY: process.env.NEXT_PUBLIC_TOKEN_STORAGE_KEY,\n    APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,\n    APP_DESCRIPTION: process.env.NEXT_PUBLIC_APP_DESCRIPTION,\n    NODE_ENV: process.env.NODE_ENV,\n    ENABLE_EMAIL_VERIFICATION: process.env.NEXT_PUBLIC_ENABLE_EMAIL_VERIFICATION === \"true\",\n    ENABLE_SOCIAL_AUTH: process.env.NEXT_PUBLIC_ENABLE_SOCIAL_AUTH === \"true\",\n  };\n\n  try {\n    return configSchema.parse(env);\n  } catch (error) {\n    console.error(\"Invalid configuration:\", error);\n    throw new Error(\"Configuration validation failed\");\n  }\n};\n\n// Export validated configuration\nexport const config = parseConfig();\n\n// API endpoints builder\nexport const apiEndpoints = {\n  auth: {\n    login: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/login`,\n    register: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/register`,\n    logout: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/logout`,\n    refresh: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/refresh`,\n    verify: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/verify`,\n    resendVerification: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/resend-verification`,\n    me: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/me`,\n  },\n  users: {\n    profile: `${config.API_BASE_URL}/api/${config.API_VERSION}/users/me`,\n    update: `${config.API_BASE_URL}/api/${config.API_VERSION}/users/me`,\n  },\n} as const;\n\n// Type for configuration\nexport type Config = z.infer<typeof configSchema>;\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;AACvC;;AAEA,mCAAmC;AACnC,MAAM,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,oBAAoB;IACpB,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC;IACvC,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAEhC,iBAAiB;IACjB,kBAAkB,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IACrC,mBAAmB,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAEtC,cAAc;IACd,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC7B,iBAAiB,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAEpC,cAAc;IACd,UAAU,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAEhE,WAAW;IACX,2BAA2B,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/C,oBAAoB,6KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1C;AAEA,2CAA2C;AAC3C,MAAM,cAAc;IAClB,MAAM,MAAM;QACV,YAAY;QACZ,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QACjB,QAAQ;QACR,eAAe;QACf,QAAQ;QACR,2BAA2B,6CAAsD;QACjF,oBAAoB,8CAA+C;IACrE;IAEA,IAAI;QACF,OAAO,aAAa,KAAK,CAAC;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,MAAM,SAAS;AAGf,MAAM,eAAe;IAC1B,MAAM;QACJ,OAAO,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,WAAW,CAAC;QACpE,UAAU,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,cAAc,CAAC;QAC1E,QAAQ,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,YAAY,CAAC;QACtE,SAAS,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,aAAa,CAAC;QACxE,QAAQ,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,YAAY,CAAC;QACtE,oBAAoB,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,yBAAyB,CAAC;QAC/F,IAAI,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,QAAQ,CAAC;IAChE;IACA,OAAO;QACL,SAAS,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,SAAS,CAAC;QACpE,QAAQ,GAAG,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,WAAW,CAAC,SAAS,CAAC;IACrE;AACF", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nextjs/webapp/frontend/src/lib/validations.ts"], "sourcesContent": ["// Form validation schemas using Zod\nimport { z } from \"zod\";\n\n// Authentication schemas\nexport const loginSchema = z.object({\n  email: z\n    .string()\n    .min(1, \"Email is required\")\n    .email(\"Please enter a valid email address\"),\n  password: z\n    .string()\n    .min(1, \"Password is required\")\n    .min(8, \"Password must be at least 8 characters\"),\n});\n\nexport const signupSchema = z.object({\n  email: z\n    .string()\n    .min(1, \"Email is required\")\n    .email(\"Please enter a valid email address\"),\n  password: z\n    .string()\n    .min(8, \"Password must be at least 8 characters\")\n    .regex(\n      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/,\n      \"Password must contain at least one uppercase letter, one lowercase letter, and one number\"\n    ),\n  confirmPassword: z.string().min(1, \"Please confirm your password\"),\n  full_name: z.string().optional(),\n}).refine((data) => data.password === data.confirmPassword, {\n  message: \"Passwords don't match\",\n  path: [\"confirmPassword\"],\n});\n\nexport const emailVerificationSchema = z.object({\n  token: z.string().min(1, \"Verification token is required\"),\n});\n\nexport const resendVerificationSchema = z.object({\n  email: z\n    .string()\n    .min(1, \"Email is required\")\n    .email(\"Please enter a valid email address\"),\n});\n\nexport const passwordResetRequestSchema = z.object({\n  email: z\n    .string()\n    .min(1, \"Email is required\")\n    .email(\"Please enter a valid email address\"),\n});\n\nexport const passwordResetSchema = z.object({\n  token: z.string().min(1, \"Reset token is required\"),\n  password: z\n    .string()\n    .min(8, \"Password must be at least 8 characters\")\n    .regex(\n      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/,\n      \"Password must contain at least one uppercase letter, one lowercase letter, and one number\"\n    ),\n  confirmPassword: z.string().min(1, \"Please confirm your password\"),\n}).refine((data) => data.password === data.confirmPassword, {\n  message: \"Passwords don't match\",\n  path: [\"confirmPassword\"],\n});\n\n// Profile update schema\nexport const profileUpdateSchema = z.object({\n  full_name: z.string().min(1, \"Full name is required\").optional(),\n  email: z\n    .string()\n    .min(1, \"Email is required\")\n    .email(\"Please enter a valid email address\"),\n});\n\n// Type exports\nexport type LoginFormData = z.infer<typeof loginSchema>;\nexport type SignupFormData = z.infer<typeof signupSchema>;\nexport type EmailVerificationData = z.infer<typeof emailVerificationSchema>;\nexport type ResendVerificationData = z.infer<typeof resendVerificationSchema>;\nexport type PasswordResetRequestData = z.infer<typeof passwordResetRequestSchema>;\nexport type PasswordResetData = z.infer<typeof passwordResetSchema>;\nexport type ProfileUpdateData = z.infer<typeof profileUpdateSchema>;\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;;;;;;AACpC;;AAGO,MAAM,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,6KAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,qBACP,KAAK,CAAC;IACT,UAAU,6KAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,wBACP,GAAG,CAAC,GAAG;AACZ;AAEO,MAAM,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO,6KAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,qBACP,KAAK,CAAC;IACT,UAAU,6KAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,0CACP,KAAK,CACJ,mCACA;IAEJ,iBAAiB,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACnC,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAChC,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAEO,MAAM,0BAA0B,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC3B;AAEO,MAAM,2BAA2B,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/C,OAAO,6KAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,qBACP,KAAK,CAAC;AACX;AAEO,MAAM,6BAA6B,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjD,OAAO,6KAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,qBACP,KAAK,CAAC;AACX;AAEO,MAAM,sBAAsB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,UAAU,6KAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,0CACP,KAAK,CACJ,mCACA;IAEJ,iBAAiB,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACrC,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGO,MAAM,sBAAsB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,QAAQ;IAC9D,OAAO,6KAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,qBACP,KAAK,CAAC;AACX", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nextjs/webapp/frontend/src/lib/actions/auth-actions.ts"], "sourcesContent": ["\"use server\";\n\nimport { redirect } from \"next/navigation\";\nimport { cookies } from \"next/headers\";\nimport { apiClient, apiUtils } from \"@/lib/api-client\";\nimport { apiEndpoints, config } from \"@/lib/config\";\nimport { loginSchema, signupSchema, type LoginFormData, type SignupFormData } from \"@/lib/validations\";\nimport type { FormState, AuthTokens } from \"@/types\";\n\n// Login action\nexport async function loginAction(\n  prevState: FormState,\n  formData: FormData\n): Promise<FormState> {\n  try {\n    // Extract and validate form data\n    const rawData = {\n      email: formData.get(\"email\") as string,\n      password: formData.get(\"password\") as string,\n    };\n\n    const validatedData = loginSchema.parse(rawData);\n\n    // Make API call to login endpoint\n    const response = await fetch(apiEndpoints.auth.login, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/x-www-form-urlencoded\",\n      },\n      body: new URLSearchParams({\n        username: validatedData.email, // FastAPI expects 'username' field\n        password: validatedData.password,\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return {\n        success: false,\n        message: errorData.detail || \"Login failed\",\n        errors: {},\n      };\n    }\n\n    const tokens: AuthTokens = await response.json();\n\n    // Set session cookie for SSR compatibility\n    const cookieStore = await cookies();\n    cookieStore.set(config.AUTH_COOKIE_NAME, tokens.access_token, {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === \"production\",\n      sameSite: \"lax\",\n      maxAge: 60 * 60 * 24 * 7, // 7 days\n    });\n\n    // Note: Client-side token storage will be handled by the login form component\n    // after successful server action completion\n\n    return {\n      success: true,\n      message: \"Login successful\",\n      tokens, // Pass tokens to client for localStorage storage\n    };\n  } catch (error: any) {\n    if (error.name === \"ZodError\") {\n      const fieldErrors: Record<string, string[]> = {};\n      error.errors.forEach((err: any) => {\n        const field = err.path[0];\n        if (!fieldErrors[field]) {\n          fieldErrors[field] = [];\n        }\n        fieldErrors[field].push(err.message);\n      });\n\n      return {\n        success: false,\n        message: \"Validation failed\",\n        errors: fieldErrors,\n      };\n    }\n\n    return {\n      success: false,\n      message: error.message || \"An unexpected error occurred\",\n      errors: {},\n    };\n  }\n}\n\n// Signup action\nexport async function signupAction(\n  prevState: FormState,\n  formData: FormData\n): Promise<FormState> {\n  try {\n    // Extract and validate form data\n    const rawData = {\n      email: formData.get(\"email\") as string,\n      password: formData.get(\"password\") as string,\n      confirmPassword: formData.get(\"confirmPassword\") as string,\n      full_name: formData.get(\"full_name\") as string || undefined,\n    };\n\n    const validatedData = signupSchema.parse(rawData);\n\n    // Make API call to register endpoint\n    const response = await fetch(apiEndpoints.auth.register, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({\n        email: validatedData.email,\n        password: validatedData.password,\n        full_name: validatedData.full_name,\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      return {\n        success: false,\n        message: errorData.detail || \"Registration failed\",\n        errors: {},\n      };\n    }\n\n    return {\n      success: true,\n      message: \"Registration successful! Please check your email to verify your account.\",\n    };\n  } catch (error: any) {\n    if (error.name === \"ZodError\") {\n      const fieldErrors: Record<string, string[]> = {};\n      error.errors.forEach((err: any) => {\n        const field = err.path[0];\n        if (!fieldErrors[field]) {\n          fieldErrors[field] = [];\n        }\n        fieldErrors[field].push(err.message);\n      });\n\n      return {\n        success: false,\n        message: \"Validation failed\",\n        errors: fieldErrors,\n      };\n    }\n\n    return {\n      success: false,\n      message: error.message || \"An unexpected error occurred\",\n      errors: {},\n    };\n  }\n}\n\n// Logout action\nexport async function logoutAction(): Promise<void> {\n  try {\n    // Clear session cookie\n    const cookieStore = await cookies();\n    cookieStore.delete(config.AUTH_COOKIE_NAME);\n\n    // Try to call logout endpoint (optional, as cookie is already cleared)\n    try {\n      await fetch(apiEndpoints.auth.logout, {\n        method: \"POST\",\n        credentials: \"include\",\n      });\n    } catch (error) {\n      // Ignore logout API errors as cookie is already cleared\n      console.warn(\"Logout API call failed:\", error);\n    }\n  } catch (error) {\n    console.error(\"Logout action failed:\", error);\n  }\n\n  // Redirect to login page\n  redirect(\"/login\");\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAAA;AACA;AAEA;AACA;;;;;;;AAIO,eAAe,YACpB,SAAoB,EACpB,QAAkB;IAElB,IAAI;QACF,iCAAiC;QACjC,MAAM,UAAU;YACd,OAAO,SAAS,GAAG,CAAC;YACpB,UAAU,SAAS,GAAG,CAAC;QACzB;QAEA,MAAM,gBAAgB,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC;QAExC,kCAAkC;QAClC,MAAM,WAAW,MAAM,MAAM,oHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,KAAK,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,IAAI,gBAAgB;gBACxB,UAAU,cAAc,KAAK;gBAC7B,UAAU,cAAc,QAAQ;YAClC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,OAAO;gBACL,SAAS;gBACT,SAAS,UAAU,MAAM,IAAI;gBAC7B,QAAQ,CAAC;YACX;QACF;QAEA,MAAM,SAAqB,MAAM,SAAS,IAAI;QAE9C,2CAA2C;QAC3C,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAChC,YAAY,GAAG,CAAC,oHAAA,CAAA,SAAM,CAAC,gBAAgB,EAAE,OAAO,YAAY,EAAE;YAC5D,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,KAAK,KAAK,KAAK;QACzB;QAEA,8EAA8E;QAC9E,4CAA4C;QAE5C,OAAO;YACL,SAAS;YACT,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAY;QACnB,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,MAAM,cAAwC,CAAC;YAC/C,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;gBACpB,MAAM,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACzB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;oBACvB,WAAW,CAAC,MAAM,GAAG,EAAE;gBACzB;gBACA,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO;YACrC;YAEA,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,QAAQ;YACV;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS,MAAM,OAAO,IAAI;YAC1B,QAAQ,CAAC;QACX;IACF;AACF;AAGO,eAAe,aACpB,SAAoB,EACpB,QAAkB;IAElB,IAAI;QACF,iCAAiC;QACjC,MAAM,UAAU;YACd,OAAO,SAAS,GAAG,CAAC;YACpB,UAAU,SAAS,GAAG,CAAC;YACvB,iBAAiB,SAAS,GAAG,CAAC;YAC9B,WAAW,SAAS,GAAG,CAAC,gBAA0B;QACpD;QAEA,MAAM,gBAAgB,yHAAA,CAAA,eAAY,CAAC,KAAK,CAAC;QAEzC,qCAAqC;QACrC,MAAM,WAAW,MAAM,MAAM,oHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,cAAc,KAAK;gBAC1B,UAAU,cAAc,QAAQ;gBAChC,WAAW,cAAc,SAAS;YACpC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,OAAO;gBACL,SAAS;gBACT,SAAS,UAAU,MAAM,IAAI;gBAC7B,QAAQ,CAAC;YACX;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAY;QACnB,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,MAAM,cAAwC,CAAC;YAC/C,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;gBACpB,MAAM,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACzB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;oBACvB,WAAW,CAAC,MAAM,GAAG,EAAE;gBACzB;gBACA,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO;YACrC;YAEA,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,QAAQ;YACV;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS,MAAM,OAAO,IAAI;YAC1B,QAAQ,CAAC;QACX;IACF;AACF;AAGO,eAAe;IACpB,IAAI;QACF,uBAAuB;QACvB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAChC,YAAY,MAAM,CAAC,oHAAA,CAAA,SAAM,CAAC,gBAAgB;QAE1C,uEAAuE;QACvE,IAAI;YACF,MAAM,MAAM,oHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM,EAAE;gBACpC,QAAQ;gBACR,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,wDAAwD;YACxD,QAAQ,IAAI,CAAC,2BAA2B;QAC1C;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;IACzC;IAEA,yBAAyB;IACzB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AACX;;;IA1KsB;IAgFA;IAoEA;;AApJA,+OAAA;AAgFA,+OAAA;AAoEA,+OAAA", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nextjs/webapp/frontend/.next-internal/server/app/%28auth%29/signup/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {signupAction as '60bbd616165892379a16474ad8c6fde51fe844c9ab'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nextjs/webapp/frontend/src/components/auth/signup-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const SignupForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignupForm() from the server but SignupForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/signup-form.tsx <module evaluation>\",\n    \"SignupForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,qEACA", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nextjs/webapp/frontend/src/components/auth/signup-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const SignupForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignupForm() from the server but SignupForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/signup-form.tsx\",\n    \"SignupForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,iDACA", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nextjs/webapp/frontend/src/app/%28auth%29/signup/page.tsx"], "sourcesContent": ["import { SignupForm } from \"@/components/auth/signup-form\";\nimport { Metadata } from \"next\";\n\nexport const metadata: Metadata = {\n  title: \"Sign Up - WebApp\",\n  description: \"Create a new account to get started\",\n};\n\nexport default function SignupPage() {\n  return <SignupForm />;\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBAAO,8OAAC,4IAAA,CAAA,aAAU;;;;;AACpB", "debugId": null}}]}