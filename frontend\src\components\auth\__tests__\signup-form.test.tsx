import { screen, waitFor } from '@testing-library/react'
import { SignupForm } from '../signup-form'
import { renderWithProviders, fillForm, submitForm } from '@/lib/__tests__/test-utils'

// Mock the auth actions
jest.mock('@/lib/actions/auth-actions', () => ({
  signupAction: jest.fn(),
}))

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}))

describe('SignupForm', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders signup form with all required fields', () => {
    renderWithProviders(<SignupForm />)

    expect(screen.getByRole('heading', { name: /create your account/i })).toBeInTheDocument()
    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/^email$/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument()
    expect(screen.getByText(/already have an account/i)).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /sign in/i })).toBeInTheDocument()
  })

  it('shows full name as optional', () => {
    renderWithProviders(<SignupForm />)

    const fullNameLabel = screen.getByText(/full name \(optional\)/i)
    expect(fullNameLabel).toBeInTheDocument()
    
    const fullNameInput = screen.getByLabelText(/full name/i)
    expect(fullNameInput).not.toBeRequired()
  })

  it('marks required fields as required', () => {
    renderWithProviders(<SignupForm />)

    expect(screen.getByLabelText(/^email$/i)).toBeRequired()
    expect(screen.getByLabelText(/^password$/i)).toBeRequired()
    expect(screen.getByLabelText(/confirm password/i)).toBeRequired()
  })

  it('submits form with valid data', async () => {
    const mockSignupAction = require('@/lib/actions/auth-actions').signupAction
    mockSignupAction.mockResolvedValue({
      success: true,
      message: 'Account created successfully',
    })

    renderWithProviders(<SignupForm />)

    await fillForm(screen.getByLabelText, {
      'full name': 'John Doe',
      email: '<EMAIL>',
      password: 'password123',
      'confirm password': 'password123',
    })

    await submitForm(screen.getByRole, /create account/i)

    await waitFor(() => {
      expect(mockSignupAction).toHaveBeenCalled()
    })
  })

  it('submits form without full name', async () => {
    const mockSignupAction = require('@/lib/actions/auth-actions').signupAction
    mockSignupAction.mockResolvedValue({
      success: true,
      message: 'Account created successfully',
    })

    renderWithProviders(<SignupForm />)

    await fillForm(screen.getByLabelText, {
      email: '<EMAIL>',
      password: 'password123',
      'confirm password': 'password123',
    })

    await submitForm(screen.getByRole, /create account/i)

    await waitFor(() => {
      expect(mockSignupAction).toHaveBeenCalled()
    })
  })

  it('displays error message on signup failure', async () => {
    const mockSignupAction = require('@/lib/actions/auth-actions').signupAction
    mockSignupAction.mockResolvedValue({
      success: false,
      message: 'Email already exists',
      errors: {},
    })

    renderWithProviders(<SignupForm />)

    await fillForm(screen.getByLabelText, {
      email: '<EMAIL>',
      password: 'password123',
      'confirm password': 'password123',
    })

    await submitForm(screen.getByRole, /create account/i)

    await waitFor(() => {
      expect(screen.getByText(/email already exists/i)).toBeInTheDocument()
    })
  })

  it('displays field-specific validation errors', async () => {
    const mockSignupAction = require('@/lib/actions/auth-actions').signupAction
    mockSignupAction.mockResolvedValue({
      success: false,
      message: '',
      errors: {
        email: ['Please enter a valid email address'],
        password: ['Password must be at least 8 characters'],
        confirmPassword: ['Passwords do not match'],
      },
    })

    renderWithProviders(<SignupForm />)

    await fillForm(screen.getByLabelText, {
      email: 'invalid-email',
      password: '123',
      'confirm password': '456',
    })

    await submitForm(screen.getByRole, /create account/i)

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument()
      expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument()
      expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument()
    })
  })

  it('displays success message and redirects after successful signup', async () => {
    const mockSignupAction = require('@/lib/actions/auth-actions').signupAction
    mockSignupAction.mockResolvedValue({
      success: true,
      message: 'Account created successfully! Please check your email.',
    })

    renderWithProviders(<SignupForm />)

    await fillForm(screen.getByLabelText, {
      email: '<EMAIL>',
      password: 'password123',
      'confirm password': 'password123',
    })

    await submitForm(screen.getByRole, /create account/i)

    await waitFor(() => {
      expect(screen.getByText(/account created successfully/i)).toBeInTheDocument()
    })
  })

  it('disables form during submission', async () => {
    const mockSignupAction = require('@/lib/actions/auth-actions').signupAction
    // Mock a delayed response
    mockSignupAction.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))

    renderWithProviders(<SignupForm />)

    await fillForm(screen.getByLabelText, {
      email: '<EMAIL>',
      password: 'password123',
      'confirm password': 'password123',
    })

    const submitButton = screen.getByRole('button', { name: /create account/i })
    const inputs = [
      screen.getByLabelText(/full name/i),
      screen.getByLabelText(/^email$/i),
      screen.getByLabelText(/^password$/i),
      screen.getByLabelText(/confirm password/i),
    ]

    await submitForm(screen.getByRole, /create account/i)

    // Check that form is disabled during submission
    expect(submitButton).toBeDisabled()
    inputs.forEach(input => expect(input).toBeDisabled())
    expect(screen.getByText(/creating account/i)).toBeInTheDocument()
  })

  it('has terms and privacy links', () => {
    renderWithProviders(<SignupForm />)

    expect(screen.getByRole('link', { name: /terms of service/i })).toHaveAttribute('href', '/terms')
    expect(screen.getByRole('link', { name: /privacy policy/i })).toHaveAttribute('href', '/privacy')
  })
})
