2025-08-01 21:52:24 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 21:52:24 - uvicorn.error - INFO - server - _serve:84 - Started server process [19532]
2025-08-01 21:52:24 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 21:52:24 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 21:52:24 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 21:52:24 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 21:52:35 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 21:52:35 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 21:52:35 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 21:52:35 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 21:52:35 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 21:52:35 - uvicorn.error - INFO - server - _serve:94 - Finished server process [19532]
2025-08-01 21:52:36 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 21:52:36 - uvicorn.error - INFO - server - _serve:84 - Started server process [1584]
2025-08-01 21:52:36 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 21:52:36 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 21:52:36 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 21:52:36 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 21:53:39 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 21:53:39 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 21:53:39 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 21:53:39 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 21:53:39 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 21:53:39 - uvicorn.error - INFO - server - _serve:94 - Finished server process [1584]
2025-08-01 21:53:40 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 21:53:40 - uvicorn.error - INFO - server - _serve:84 - Started server process [20364]
2025-08-01 21:53:40 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 21:53:40 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 21:53:40 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 21:53:40 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 21:53:59 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 21:53:59 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 21:53:59 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 21:53:59 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 21:53:59 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 21:53:59 - uvicorn.error - INFO - server - _serve:94 - Finished server process [20364]
2025-08-01 22:01:22 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:01:22 - uvicorn.error - INFO - server - _serve:84 - Started server process [18760]
2025-08-01 22:01:22 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:01:22 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:01:22 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:01:22 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:01:33 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:01:33 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:01:33 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:01:33 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:01:33 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:01:33 - uvicorn.error - INFO - server - _serve:94 - Finished server process [18760]
2025-08-01 22:01:35 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:01:35 - uvicorn.error - INFO - server - _serve:84 - Started server process [14072]
2025-08-01 22:01:35 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:01:35 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:01:35 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:01:35 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:02:32 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:03:06 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:03:06 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:03:06 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:03:06 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:03:06 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:03:06 - uvicorn.error - INFO - server - _serve:94 - Finished server process [14072]
2025-08-01 22:03:08 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:03:08 - uvicorn.error - INFO - server - _serve:84 - Started server process [17020]
2025-08-01 22:03:08 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:03:08 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:03:08 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:03:08 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:03:18 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:03:18 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:03:18 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:03:18 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:03:18 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:03:18 - uvicorn.error - INFO - server - _serve:94 - Finished server process [17020]
2025-08-01 22:03:19 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:03:19 - uvicorn.error - INFO - server - _serve:84 - Started server process [6180]
2025-08-01 22:03:19 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:03:19 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:03:19 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:03:19 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:04:44 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:07:28 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:07:28 - passlib.handlers.bcrypt - WARNING - bcrypt - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 22:08:22 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:08:22 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:08:22 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:08:22 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:08:22 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:08:22 - uvicorn.error - INFO - server - _serve:94 - Finished server process [6180]
2025-08-01 22:08:24 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:08:24 - uvicorn.error - INFO - server - _serve:84 - Started server process [17212]
2025-08-01 22:08:24 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:08:24 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:08:24 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:08:24 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:08:43 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:08:43 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:08:43 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:08:43 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:08:43 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:08:43 - uvicorn.error - INFO - server - _serve:94 - Finished server process [17212]
2025-08-01 22:08:44 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:08:44 - uvicorn.error - INFO - server - _serve:84 - Started server process [8116]
2025-08-01 22:08:44 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:08:44 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:08:44 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:08:44 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:09:06 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:09:06 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:09:06 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:09:06 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:09:06 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:09:06 - uvicorn.error - INFO - server - _serve:94 - Finished server process [8116]
2025-08-01 22:09:07 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:09:07 - uvicorn.error - INFO - server - _serve:84 - Started server process [17440]
2025-08-01 22:09:07 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:09:07 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:09:08 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:09:08 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:09:21 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:09:21 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:09:21 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:09:21 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:09:21 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:09:21 - uvicorn.error - INFO - server - _serve:94 - Finished server process [17440]
2025-08-01 22:09:23 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:09:23 - uvicorn.error - INFO - server - _serve:84 - Started server process [4832]
2025-08-01 22:09:23 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:09:23 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:09:23 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:09:23 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:10:26 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:10:26 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:10:26 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:10:26 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:10:26 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:10:26 - uvicorn.error - INFO - server - _serve:94 - Finished server process [4832]
2025-08-01 22:10:27 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:10:28 - uvicorn.error - INFO - server - _serve:84 - Started server process [14900]
2025-08-01 22:10:28 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:10:28 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:10:28 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:10:28 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:10:42 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:10:42 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:10:42 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:10:42 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:10:42 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:10:42 - uvicorn.error - INFO - server - _serve:94 - Finished server process [14900]
2025-08-01 22:10:44 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:10:44 - uvicorn.error - INFO - server - _serve:84 - Started server process [16684]
2025-08-01 22:10:44 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:10:44 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:10:44 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:10:44 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:11:20 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:11:20 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:11:20 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:11:20 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:11:20 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:11:20 - uvicorn.error - INFO - server - _serve:94 - Finished server process [16684]
2025-08-01 22:11:21 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:11:21 - uvicorn.error - INFO - server - _serve:84 - Started server process [10108]
2025-08-01 22:11:21 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:11:21 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:11:22 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:11:22 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:11:45 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:11:45 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:11:45 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:11:45 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:11:45 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:11:45 - uvicorn.error - INFO - server - _serve:94 - Finished server process [10108]
2025-08-01 22:11:46 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:11:46 - uvicorn.error - INFO - server - _serve:84 - Started server process [5776]
2025-08-01 22:11:46 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:11:46 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:11:47 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:11:47 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:12:04 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:12:04 - passlib.handlers.bcrypt - WARNING - bcrypt - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 22:12:18 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:12:18 - passlib.handlers.bcrypt - WARNING - bcrypt - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 22:13:28 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:13:28 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:13:28 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:13:28 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:13:28 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:13:28 - uvicorn.error - INFO - server - _serve:94 - Finished server process [5776]
2025-08-01 22:13:29 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:13:29 - uvicorn.error - INFO - server - _serve:84 - Started server process [4704]
2025-08-01 22:13:29 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:13:29 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:13:29 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:13:29 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:15:11 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:15:12 - passlib.handlers.bcrypt - WARNING - bcrypt - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 22:15:23 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:15:23 - passlib.handlers.bcrypt - WARNING - bcrypt - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 22:16:12 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:16:12 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:16:12 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:16:12 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:16:12 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:16:12 - uvicorn.error - INFO - server - _serve:94 - Finished server process [4704]
2025-08-01 22:16:13 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:16:13 - uvicorn.error - INFO - server - _serve:84 - Started server process [17832]
2025-08-01 22:16:13 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:16:13 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:16:14 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:16:14 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:16:37 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:16:37 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:16:37 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:16:37 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:16:37 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:16:37 - uvicorn.error - INFO - server - _serve:94 - Finished server process [17832]
2025-08-01 22:16:39 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:16:39 - uvicorn.error - INFO - server - _serve:84 - Started server process [14128]
2025-08-01 22:16:39 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:16:39 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:16:39 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:16:39 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:17:05 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:17:05 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:17:05 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:17:05 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:17:05 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:17:05 - uvicorn.error - INFO - server - _serve:94 - Finished server process [14128]
2025-08-01 22:17:06 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:17:06 - uvicorn.error - INFO - server - _serve:84 - Started server process [10040]
2025-08-01 22:17:06 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:17:06 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:17:06 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:17:06 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:17:26 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:17:26 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:17:26 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:17:26 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:17:26 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:17:26 - uvicorn.error - INFO - server - _serve:94 - Finished server process [10040]
2025-08-01 22:17:28 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:17:28 - uvicorn.error - INFO - server - _serve:84 - Started server process [11848]
2025-08-01 22:17:28 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:17:28 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:17:28 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:17:28 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:17:38 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:17:38 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:17:38 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:17:38 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:17:38 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:17:38 - uvicorn.error - INFO - server - _serve:94 - Finished server process [11848]
2025-08-01 22:17:40 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:17:40 - uvicorn.error - INFO - server - _serve:84 - Started server process [2548]
2025-08-01 22:17:40 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:17:40 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:17:40 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:17:40 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:18:07 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:18:07 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:18:07 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:18:07 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:18:07 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:18:07 - uvicorn.error - INFO - server - _serve:94 - Finished server process [2548]
2025-08-01 22:18:09 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:18:09 - uvicorn.error - INFO - server - _serve:84 - Started server process [20352]
2025-08-01 22:18:09 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:18:09 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:18:09 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:18:09 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:18:31 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:18:31 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:18:31 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:18:31 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:18:31 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:18:31 - uvicorn.error - INFO - server - _serve:94 - Finished server process [20352]
2025-08-01 22:18:33 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:18:33 - uvicorn.error - INFO - server - _serve:84 - Started server process [20380]
2025-08-01 22:18:33 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:18:33 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:18:33 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:18:33 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:19:07 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:19:07 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:19:07 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:19:07 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:19:07 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:19:07 - uvicorn.error - INFO - server - _serve:94 - Finished server process [20380]
2025-08-01 22:19:09 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:19:09 - uvicorn.error - INFO - server - _serve:84 - Started server process [21084]
2025-08-01 22:19:09 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:19:09 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:19:09 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:19:09 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:19:22 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:19:22 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:19:22 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:19:22 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:19:22 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:19:22 - uvicorn.error - INFO - server - _serve:94 - Finished server process [21084]
2025-08-01 22:19:24 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:19:24 - uvicorn.error - INFO - server - _serve:84 - Started server process [4512]
2025-08-01 22:19:24 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:19:24 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:19:24 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:19:24 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:19:47 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:19:47 - passlib.handlers.bcrypt - WARNING - bcrypt - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 22:20:06 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:20:06 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:20:06 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:20:06 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:20:06 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:20:06 - uvicorn.error - INFO - server - _serve:94 - Finished server process [4512]
2025-08-01 22:20:08 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:20:08 - uvicorn.error - INFO - server - _serve:84 - Started server process [20488]
2025-08-01 22:20:08 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:20:08 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:20:08 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:20:08 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:20:33 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:20:33 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:20:33 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:20:33 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:20:33 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:20:33 - uvicorn.error - INFO - server - _serve:94 - Finished server process [20488]
2025-08-01 22:20:35 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:20:35 - uvicorn.error - INFO - server - _serve:84 - Started server process [17368]
2025-08-01 22:20:35 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:20:35 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:20:35 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:20:35 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:20:56 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:20:56 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:20:56 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:20:56 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:20:56 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:20:56 - uvicorn.error - INFO - server - _serve:94 - Finished server process [17368]
2025-08-01 22:20:57 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:20:57 - uvicorn.error - INFO - server - _serve:84 - Started server process [4552]
2025-08-01 22:20:57 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:20:57 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:20:57 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:20:57 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:21:07 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:21:08 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:21:08 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:21:08 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:21:08 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:21:08 - uvicorn.error - INFO - server - _serve:94 - Finished server process [4552]
2025-08-01 22:21:09 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:21:09 - uvicorn.error - INFO - server - _serve:84 - Started server process [21336]
2025-08-01 22:21:09 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:21:09 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:21:09 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:21:09 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:22:45 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:22:45 - passlib.handlers.bcrypt - WARNING - bcrypt - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 22:23:28 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:23:28 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:23:28 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:23:28 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:23:28 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:23:28 - uvicorn.error - INFO - server - _serve:94 - Finished server process [21336]
2025-08-01 22:23:29 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:23:29 - uvicorn.error - INFO - server - _serve:84 - Started server process [18656]
2025-08-01 22:23:29 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:23:29 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:23:29 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:23:29 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:24:16 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:24:16 - passlib.handlers.bcrypt - WARNING - bcrypt - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 22:25:04 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:25:05 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:25:05 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:25:05 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:25:05 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:25:05 - uvicorn.error - INFO - server - _serve:94 - Finished server process [18656]
2025-08-01 22:25:06 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:25:06 - uvicorn.error - INFO - server - _serve:84 - Started server process [18364]
2025-08-01 22:25:06 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:25:06 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:25:06 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:25:06 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:25:33 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:25:33 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:25:33 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:25:33 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:25:33 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:25:33 - uvicorn.error - INFO - server - _serve:94 - Finished server process [18364]
2025-08-01 22:25:34 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:25:34 - uvicorn.error - INFO - server - _serve:84 - Started server process [17656]
2025-08-01 22:25:34 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:25:34 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:25:34 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:25:34 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:26:23 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:26:23 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:26:23 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:26:23 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:26:23 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:26:23 - uvicorn.error - INFO - server - _serve:94 - Finished server process [17656]
2025-08-01 22:26:25 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:26:25 - uvicorn.error - INFO - server - _serve:84 - Started server process [21432]
2025-08-01 22:26:25 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:26:25 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:26:25 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:26:25 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:27:07 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:27:07 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:27:07 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:27:07 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:27:07 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:27:07 - uvicorn.error - INFO - server - _serve:94 - Finished server process [21432]
2025-08-01 22:27:09 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:27:09 - uvicorn.error - INFO - server - _serve:84 - Started server process [5816]
2025-08-01 22:27:09 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:27:09 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:27:09 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:27:09 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:29:19 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:29:19 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:29:19 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:29:19 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:29:19 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:29:19 - uvicorn.error - INFO - server - _serve:94 - Finished server process [5816]
2025-08-01 22:29:21 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:29:21 - uvicorn.error - INFO - server - _serve:84 - Started server process [20700]
2025-08-01 22:29:21 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:29:21 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:29:21 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:29:21 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:29:36 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:29:36 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:29:36 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:29:36 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:29:36 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:29:36 - uvicorn.error - INFO - server - _serve:94 - Finished server process [20700]
2025-08-01 22:29:38 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:29:38 - uvicorn.error - INFO - server - _serve:84 - Started server process [20196]
2025-08-01 22:29:38 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:29:38 - app.main - INFO - main - lifespan:23 - Starting up FastAPI application...
2025-08-01 22:29:38 - app.main - INFO - main - lifespan:30 - FastAPI application started successfully
2025-08-01 22:29:38 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:30:00 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:30:00 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:30:00 - app.main - INFO - main - lifespan:35 - Shutting down FastAPI application...
2025-08-01 22:30:00 - app.main - INFO - main - lifespan:37 - FastAPI application shut down successfully
2025-08-01 22:30:00 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:30:00 - uvicorn.error - INFO - server - _serve:94 - Finished server process [20196]
2025-08-01 22:37:21 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:37:21 - uvicorn.error - INFO - server - _serve:84 - Started server process [8200]
2025-08-01 22:37:21 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:37:21 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:37:21 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:37:21 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:37:21 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:37:21 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:37:21 - app.api - INFO - logging - log_api_request:220 - GET /
2025-08-01 22:37:21 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:37:30 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:37:30 - passlib.handlers.bcrypt - WARNING - bcrypt - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 22:37:50 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:38:30 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:38:30 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:38:30 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:38:30 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:38:30 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:38:30 - uvicorn.error - INFO - server - _serve:94 - Finished server process [8200]
2025-08-01 22:38:32 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:38:32 - uvicorn.error - INFO - server - _serve:84 - Started server process [19964]
2025-08-01 22:38:32 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:38:32 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:38:32 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:38:32 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:38:32 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:38:32 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:38:56 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:38:56 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 0.001s
2025-08-01 22:38:56 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:56 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.001s
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 400 Bad Request"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:38:57 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 0.001s
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:39:09 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:39:09 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:39:09 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:39:09 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:39:38 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:39:38 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:39:38 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:39:38 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:39:38 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:39:38 - uvicorn.error - INFO - server - _serve:94 - Finished server process [19964]
2025-08-01 22:41:23 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:41:23 - uvicorn.error - INFO - server - _serve:84 - Started server process [4792]
2025-08-01 22:41:23 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:41:23 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:41:23 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:41:23 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:41:23 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:41:23 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:41:42 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:41:42 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:41:42 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 404 Not Found"
2025-08-01 22:42:12 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:42:12 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/auth/me
2025-08-01 22:42:12 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/auth/me "HTTP/1.1 404 Not Found"
2025-08-01 22:42:52 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:42:52 - app.api - INFO - logging - log_api_request:220 - GET / - 0.002s
2025-08-01 22:42:52 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/ "HTTP/1.1 200 OK"
2025-08-01 22:42:52 - app.api - INFO - logging - log_api_request:220 - GET /health
2025-08-01 22:42:52 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/health "HTTP/1.1 200 OK"
2025-08-01 22:42:52 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/auth/me - 0.001s
2025-08-01 22:42:52 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/auth/me "HTTP/1.1 404 Not Found"
2025-08-01 22:42:52 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:42:52 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 404 Not Found"
2025-08-01 22:43:35 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:44:06 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:44:06 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:44:06 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:44:06 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:44:06 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:44:06 - uvicorn.error - INFO - server - _serve:94 - Finished server process [4792]
2025-08-01 22:44:07 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:44:07 - uvicorn.error - INFO - server - _serve:84 - Started server process [11096]
2025-08-01 22:44:07 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:44:07 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:44:08 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:44:08 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:44:08 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:44:08 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:44:29 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:44:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:44:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:44:43 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:44:43 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:44:43 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:44:57 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:44:58 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.142s
2025-08-01 22:44:58 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.142s
2025-08-01 22:44:58 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:45:10 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:45:11 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.130s
2025-08-01 22:45:11 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.130s
2025-08-01 22:45:11 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:45:11 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:45:11 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:45:11 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.008s
2025-08-01 22:45:11 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 22:45:11 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.005s
2025-08-01 22:45:11 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 200 OK"
2025-08-01 22:45:11 - app.app.api.v1.routes.monitoring - ERROR - monitoring - app_info:257 - App info endpoint failed: module 'psutil' has no attribute 'platform'
2025-08-01 22:45:11 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/monitoring/info: Failed to retrieve application information
2025-08-01 22:45:11 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.001s
2025-08-01 22:45:11 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:45:12 - app.app.core.cache - INFO - cache - clear:69 - Cache cleared
2025-08-01 22:45:12 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:273 - Cache cleared via monitoring endpoint
2025-08-01 22:45:12 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-01 22:45:12 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 22:45:12 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats
2025-08-01 22:45:12 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 22:45:12 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:45:12 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:45:12 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:45:12 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:45:12 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 400 Bad Request"
2025-08-01 22:45:12 - app.app.core.cache - INFO - cache - clear:69 - Cache cleared
2025-08-01 22:45:12 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:273 - Cache cleared via monitoring endpoint
2025-08-01 22:45:12 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.002s
2025-08-01 22:45:12 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 22:45:12 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.001s
2025-08-01 22:45:12 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 22:45:12 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:45:12 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:45:12 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.008s
2025-08-01 22:45:12 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 22:45:12 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - ERROR - base - _close_connection:376 - Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x0000018F15A194E0>>
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\pool\base.py", line 372, in _close_connection
    self._dialect.do_terminate(connection)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 1133, in do_terminate
    dbapi_connection.terminate()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 907, in terminate
    self.await_(asyncio.shield(self._connection.close(timeout=2)))
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg\\protocol\\protocol.pyx", line 627, in close
  File "asyncpg\\protocol\\protocol.pyx", line 660, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1673, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 436, in create_task
    self._check_closed()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 515, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-08-01 22:45:13 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.084s
2025-08-01 22:45:13 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.084s
2025-08-01 22:45:13 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:45:14 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.109s
2025-08-01 22:45:14 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.109s
2025-08-01 22:45:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:45:32 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:45:32 - app.app.api.v1.routes.monitoring - ERROR - monitoring - app_info:257 - App info endpoint failed: module 'psutil' has no attribute 'platform'
2025-08-01 22:45:32 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/monitoring/info: Failed to retrieve application information
2025-08-01 22:45:32 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.003s
2025-08-01 22:45:32 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:45:52 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:45:52 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:45:52 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:45:52 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:45:52 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:45:52 - uvicorn.error - INFO - server - _serve:94 - Finished server process [11096]
2025-08-01 22:45:59 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:45:59 - uvicorn.error - INFO - server - _serve:84 - Started server process [7368]
2025-08-01 22:45:59 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:45:59 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:45:59 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:45:59 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:45:59 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:45:59 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:46:15 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:46:15 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:46:15 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:46:15 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:46:15 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:46:15 - uvicorn.error - INFO - server - _serve:94 - Finished server process [7368]
2025-08-01 22:46:17 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:46:17 - uvicorn.error - INFO - server - _serve:84 - Started server process [16932]
2025-08-01 22:46:17 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:46:17 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:46:17 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:46:17 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:46:17 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:46:17 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:46:53 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:46:53 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.016s
2025-08-01 22:46:53 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 200 OK"
2025-08-01 22:47:47 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:47:48 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.121s
2025-08-01 22:47:48 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.121s
2025-08-01 22:47:48 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:48:02 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:48:03 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.018s
2025-08-01 22:48:03 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 200 OK"
2025-08-01 22:48:28 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.128s
2025-08-01 22:48:29 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.128s
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.008s
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.005s
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.018s
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - app.app.core.cache - INFO - cache - clear:69 - Cache cleared
2025-08-01 22:48:29 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.002s
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 400 Bad Request"
2025-08-01 22:48:29 - app.app.core.cache - INFO - cache - clear:69 - Cache cleared
2025-08-01 22:48:29 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.001s
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.007s
2025-08-01 22:48:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 22:48:29 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - ERROR - base - _close_connection:376 - Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x0000026DDECB94E0>>
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\pool\base.py", line 372, in _close_connection
    self._dialect.do_terminate(connection)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 1133, in do_terminate
    dbapi_connection.terminate()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 907, in terminate
    self.await_(asyncio.shield(self._connection.close(timeout=2)))
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg\\protocol\\protocol.pyx", line 627, in close
  File "asyncpg\\protocol\\protocol.pyx", line 660, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1673, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 436, in create_task
    self._check_closed()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 515, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-08-01 22:48:30 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.015s
2025-08-01 22:48:30 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.015s
2025-08-01 22:48:30 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:48:32 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.119s
2025-08-01 22:48:32 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.119s
2025-08-01 22:48:32 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:49:05 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:49:05 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:49:05 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:49:05 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:49:05 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:49:05 - uvicorn.error - INFO - server - _serve:94 - Finished server process [16932]
2025-08-01 22:49:06 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:49:06 - uvicorn.error - INFO - server - _serve:84 - Started server process [20980]
2025-08-01 22:49:06 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:49:06 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:49:07 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:49:07 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:49:07 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:49:07 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:49:21 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:49:22 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:49:22 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:49:22 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:49:22 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:49:22 - uvicorn.error - INFO - server - _serve:94 - Finished server process [20980]
2025-08-01 22:49:23 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:49:23 - uvicorn.error - INFO - server - _serve:84 - Started server process [12948]
2025-08-01 22:49:23 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:49:23 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:49:23 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:49:23 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:49:23 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:49:23 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:49:40 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:49:41 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.127s
2025-08-01 22:49:41 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.127s
2025-08-01 22:49:41 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:49:41 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - ERROR - base - _close_connection:376 - Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x00000180189C94E0>>
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\pool\base.py", line 372, in _close_connection
    self._dialect.do_terminate(connection)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 1133, in do_terminate
    dbapi_connection.terminate()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 907, in terminate
    self.await_(asyncio.shield(self._connection.close(timeout=2)))
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg\\protocol\\protocol.pyx", line 627, in close
  File "asyncpg\\protocol\\protocol.pyx", line 660, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1673, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 436, in create_task
    self._check_closed()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 515, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-08-01 22:49:42 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.028s
2025-08-01 22:49:42 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.028s
2025-08-01 22:49:42 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:50:07 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.130s
2025-08-01 22:50:08 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.130s
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.008s
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.010s
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.022s
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - app.app.core.cache - INFO - cache - clear:69 - Cache cleared
2025-08-01 22:50:08 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.003s
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.001s
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 400 Bad Request"
2025-08-01 22:50:08 - app.app.core.cache - INFO - cache - clear:69 - Cache cleared
2025-08-01 22:50:08 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.001s
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.011s
2025-08-01 22:50:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 22:50:08 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - ERROR - base - _close_connection:376 - Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x000001D0261BD4E0>>
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\pool\base.py", line 372, in _close_connection
    self._dialect.do_terminate(connection)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 1133, in do_terminate
    dbapi_connection.terminate()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 907, in terminate
    self.await_(asyncio.shield(self._connection.close(timeout=2)))
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg\\protocol\\protocol.pyx", line 627, in close
  File "asyncpg\\protocol\\protocol.pyx", line 660, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1673, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 436, in create_task
    self._check_closed()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 515, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-08-01 22:50:09 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.025s
2025-08-01 22:50:09 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.025s
2025-08-01 22:50:09 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:50:11 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.112s
2025-08-01 22:50:11 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.112s
2025-08-01 22:50:11 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:50:24 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:50:24 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:50:24 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:50:24 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:50:24 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:50:24 - uvicorn.error - INFO - server - _serve:94 - Finished server process [12948]
2025-08-01 22:50:26 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:50:26 - uvicorn.error - INFO - server - _serve:84 - Started server process [20648]
2025-08-01 22:50:26 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:50:26 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:50:26 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:50:26 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:50:26 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:50:26 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:50:45 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:50:46 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.236s
2025-08-01 22:50:46 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.236s
2025-08-01 22:50:46 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:50:59 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:50:59 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:00 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:00 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:00 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:00 - passlib.handlers.bcrypt - WARNING - bcrypt - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 22:51:02 - app.utils.email - ERROR - email - _send_email:48 - Failed to send <NAME_EMAIL>: Unknown error
2025-08-01 22:51:02 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/register - 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('90938a26-219e-4f47-b59c-ae77f2eeefce')}
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 328, in app
    content = await serialize_response(
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 177, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('90938a26-219e-4f47-b59c-ae77f2eeefce')}

2025-08-01 22:51:02 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('90938a26-219e-4f47-b59c-ae77f2eeefce')}

2025-08-01 22:51:02 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/register "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:02 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:02 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:02 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:02 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:02 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:02 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:05 - app.utils.email - ERROR - email - _send_email:48 - Failed to send <NAME_EMAIL>: Unknown error
2025-08-01 22:51:05 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/register - 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('95ef8a09-7ba8-4f1d-86eb-839c2ead59c6')}
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 328, in app
    content = await serialize_response(
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 177, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('95ef8a09-7ba8-4f1d-86eb-839c2ead59c6')}

2025-08-01 22:51:05 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('95ef8a09-7ba8-4f1d-86eb-839c2ead59c6')}

2025-08-01 22:51:05 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/register "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:05 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:05 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:05 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:05 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:05 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:05 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:05 - app.core.exceptions - WARNING - exceptions - validation_exception_handler:141 - Validation error on http://testserver/api/v1/auth/register: [{'type': 'value_error', 'loc': ('body', 'email'), 'msg': 'value is not a valid email address: An email address must have an @-sign.', 'input': 'invalid-email', 'ctx': {'reason': 'An email address must have an @-sign.'}}]
2025-08-01 22:51:05 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/auth/register - 0.007s
2025-08-01 22:51:05 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/register "HTTP/1.1 422 Unprocessable Entity"
2025-08-01 22:51:05 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:05 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:05 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:05 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:05 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:05 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:05 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/auth/register: Email already registered
2025-08-01 22:51:05 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/auth/register - 0.007s
2025-08-01 22:51:05 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/register "HTTP/1.1 400 Bad Request"
2025-08-01 22:51:05 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:05 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:05 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:06 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:06 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:06 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:06 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/auth/login: Incorrect email or password
2025-08-01 22:51:06 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/auth/login - 0.240s
2025-08-01 22:51:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 401 Unauthorized"
2025-08-01 22:51:06 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:06 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:06 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:06 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:06 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:06 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:06 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/auth/login: Incorrect email or password
2025-08-01 22:51:06 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/auth/login - 0.011s
2025-08-01 22:51:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 401 Unauthorized"
2025-08-01 22:51:06 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:06 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:06 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:06 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:06 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:06 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:07 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/auth/login: Incorrect email or password
2025-08-01 22:51:07 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/auth/login - 0.234s
2025-08-01 22:51:07 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 401 Unauthorized"
2025-08-01 22:51:07 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:07 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:07 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:07 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:07 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:07 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:07 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:07 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:07 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:07 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:07 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:07 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:08 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/auth/login: Incorrect email or password
2025-08-01 22:51:08 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/auth/login - 0.237s
2025-08-01 22:51:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 401 Unauthorized"
2025-08-01 22:51:08 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:08 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:08 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:08 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:08 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:08 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:08 - app.core.exceptions - WARNING - exceptions - validation_exception_handler:141 - Validation error on http://testserver/api/v1/auth/refresh: [{'type': 'missing', 'loc': ('query', 'refresh_token'), 'msg': 'Field required', 'input': None}]
2025-08-01 22:51:08 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/auth/refresh - 0.003s
2025-08-01 22:51:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/refresh "HTTP/1.1 422 Unprocessable Entity"
2025-08-01 22:51:08 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:08 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:08 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:08 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:08 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:08 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:08 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/auth/login: Incorrect email or password
2025-08-01 22:51:08 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/auth/login - 0.248s
2025-08-01 22:51:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 401 Unauthorized"
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:09 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:09 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:09 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:09 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:09 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:09 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:09 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:09 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:09 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:09 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/auth/verify-email?token=gy7lYbXnJ_0iCyDKXB_tfxvswTsSpHw3nAydnwFQ7bQ: Invalid or expired verification token
2025-08-01 22:51:09 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/auth/verify-email - 0.007s
2025-08-01 22:51:09 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/verify-email?token=gy7lYbXnJ_0iCyDKXB_tfxvswTsSpHw3nAydnwFQ7bQ "HTTP/1.1 400 Bad Request"
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:09 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:09 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:09 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/auth/verify-email?token=invalid_token: Invalid or expired verification token
2025-08-01 22:51:09 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/auth/verify-email - 0.007s
2025-08-01 22:51:09 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/verify-email?token=invalid_token "HTTP/1.1 400 Bad Request"
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:09 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:10 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:10 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:10 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:10 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:12 - app.utils.email - ERROR - email - _send_email:48 - Failed to send <NAME_EMAIL>: Unknown error
2025-08-01 22:51:12 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/auth/resend-verification - 2.036s
2025-08-01 22:51:12 - app.performance - WARNING - performance - dispatch:47 - Slow request: POST /api/v1/auth/resend-verification - 2.036s
2025-08-01 22:51:12 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/resend-verification?email=test%40example.com "HTTP/1.1 200 OK"
2025-08-01 22:51:12 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:12 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:12 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:12 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:12 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:12 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:12 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/auth/resend-verification - 0.005s
2025-08-01 22:51:12 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/resend-verification?email=nonexistent%40example.com "HTTP/1.1 200 OK"
2025-08-01 22:51:12 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:12 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:12 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:12 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:12 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:12 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:15 - app.utils.email - ERROR - email - _send_email:48 - Failed to send <NAME_EMAIL>: Unknown error
2025-08-01 22:51:15 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/register - 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('289f7e8f-27fc-47e0-a2ef-249e56b60433')}
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 328, in app
    content = await serialize_response(
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 177, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('289f7e8f-27fc-47e0-a2ef-249e56b60433')}

2025-08-01 22:51:15 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('289f7e8f-27fc-47e0-a2ef-249e56b60433')}

2025-08-01 22:51:15 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/register "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:15 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:15 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:16 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:16 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:16 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:16 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:19 - app.utils.email - ERROR - email - _send_email:48 - Failed to send <NAME_EMAIL>: Unknown error
2025-08-01 22:51:19 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/register - 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('ce497441-22d2-4c79-af2d-4b379cec2e01')}
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 328, in app
    content = await serialize_response(
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 177, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('ce497441-22d2-4c79-af2d-4b379cec2e01')}

2025-08-01 22:51:19 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('ce497441-22d2-4c79-af2d-4b379cec2e01')}

2025-08-01 22:51:19 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/register "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:19 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:19 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:19 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:19 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:19 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:19 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:19 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:19 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:19 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:19 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:19 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:19 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:19 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:19 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:19 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:19 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:19 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:20 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:20 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:20 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:20 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:20 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:20 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:20 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:20 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:20 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:20 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:20 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:20 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:20 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:20 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:20 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:20 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:20 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:20 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:20 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:20 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:20 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:20 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:20 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:20 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:20 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:20 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:20 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:20 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:20 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:20 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:20 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:20 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:20 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:21 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:21 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:21 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:21 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:21 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:21 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:21 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:25 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.120s
2025-08-01 22:51:25 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.120s
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:51:25 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:51:25 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.007s
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 22:51:25 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.004s
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 200 OK"
2025-08-01 22:51:25 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.016s
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 200 OK"
2025-08-01 22:51:25 - app.app.core.cache - INFO - cache - clear:69 - Cache cleared
2025-08-01 22:51:25 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 22:51:25 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 22:51:25 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 22:51:25 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:51:25 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 400 Bad Request"
2025-08-01 22:51:25 - app.app.core.cache - INFO - cache - clear:69 - Cache cleared
2025-08-01 22:51:25 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 22:51:25 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 22:51:25 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.002s
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 22:51:25 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:51:25 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:51:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.008s
2025-08-01 22:51:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 22:51:26 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - ERROR - base - _close_connection:376 - Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x000001B1BC89B4C0>>
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\pool\base.py", line 372, in _close_connection
    self._dialect.do_terminate(connection)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 1133, in do_terminate
    dbapi_connection.terminate()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 907, in terminate
    self.await_(asyncio.shield(self._connection.close(timeout=2)))
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg\\protocol\\protocol.pyx", line 627, in close
  File "asyncpg\\protocol\\protocol.pyx", line 660, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1673, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 436, in create_task
    self._check_closed()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 515, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-08-01 22:51:27 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.010s
2025-08-01 22:51:27 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.010s
2025-08-01 22:51:27 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:51:28 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.234s
2025-08-01 22:51:28 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.234s
2025-08-01 22:51:28 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:51:32 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:32 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - ERROR - base - _close_connection:376 - Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x000001B1BC89B790>>
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\pool\base.py", line 372, in _close_connection
    self._dialect.do_terminate(connection)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 1133, in do_terminate
    dbapi_connection.terminate()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 907, in terminate
    self.await_(asyncio.shield(self._connection.close(timeout=2)))
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg\\protocol\\protocol.pyx", line 627, in close
  File "asyncpg\\protocol\\protocol.pyx", line 660, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1673, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 436, in create_task
    self._check_closed()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 515, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-08-01 22:51:33 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:33 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:33 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:33 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:33 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/users/me: Not authenticated
2025-08-01 22:51:33 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/users/me - 0.001s
2025-08-01 22:51:33 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/users/me "HTTP/1.1 403 Forbidden"
2025-08-01 22:51:33 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:33 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:33 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:33 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:33 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:33 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:34 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:34 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:34 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:34 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:34 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:34 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:34 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:34 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:34 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:34 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:34 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:34 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:34 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:34 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:34 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:34 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:34 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:34 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:34 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:34 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:34 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:34 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:35 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:35 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:35 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:35 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:35 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:35 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:35 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:35 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:35 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:35 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:35 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:35 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:35 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:35 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:35 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:35 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:35 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:35 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:35 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:35 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:35 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:35 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:35 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:35 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:35 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:35 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:35 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:35 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:35 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:35 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:36 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:36 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:36 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:36 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:36 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:36 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:36 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:36 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:36 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:36 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:36 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:36 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:36 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:36 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:36 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:36 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:36 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:36 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:36 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:36 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:36 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:36 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:37 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:37 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:37 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:37 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:37 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:37 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:37 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:37 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:37 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:37 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:37 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:37 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:37 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:37 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:37 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:37 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:37 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:37 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:37 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:37 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:37 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:37 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:37 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:37 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:37 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:38 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:38 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:38 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:38 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:38 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:38 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:38 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:38 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:38 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:38 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:38 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:38 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:38 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:38 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:38 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:38 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:38 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:38 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:38 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:38 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:38 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:38 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:39 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:39 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:39 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:39 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:39 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:39 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:39 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:39 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:39 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:39 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:39 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:39 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:39 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:39 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:39 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:39 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:39 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:39 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:39 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:39 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:39 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:39 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:40 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:40 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:40 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:40 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:40 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:40 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:40 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:51:40 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:51:40 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:51:40 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:51:40 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:51:40 - slowapi - WARNING - extension - __evaluate_limits:510 - ratelimit 5 per 1 minute (testclient) exceeded at endpoint: /api/v1/auth/login
2025-08-01 22:51:40 - app.security - INFO - logging - log_security_event:162 - Security event: RATE_LIMIT_EXCEEDED - Endpoint: /api/v1/auth/login
2025-08-01 22:51:40 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:40 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:40 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/auth/login "HTTP/1.1 500 Internal Server Error"
2025-08-01 22:51:40 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:51:40 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:52:25 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.249s
2025-08-01 22:52:26 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.249s
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.012s
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.009s
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.016s
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - app.app.core.cache - INFO - cache - clear:69 - Cache cleared
2025-08-01 22:52:26 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.002s
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.001s
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.002s
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 400 Bad Request"
2025-08-01 22:52:26 - app.app.core.cache - INFO - cache - clear:69 - Cache cleared
2025-08-01 22:52:26 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.010s
2025-08-01 22:52:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 22:52:26 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - ERROR - base - _close_connection:376 - Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x000002854E9794E0>>
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\pool\base.py", line 372, in _close_connection
    self._dialect.do_terminate(connection)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 1133, in do_terminate
    dbapi_connection.terminate()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 907, in terminate
    self.await_(asyncio.shield(self._connection.close(timeout=2)))
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg\\protocol\\protocol.pyx", line 627, in close
  File "asyncpg\\protocol\\protocol.pyx", line 660, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1673, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 436, in create_task
    self._check_closed()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 515, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-08-01 22:52:27 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.025s
2025-08-01 22:52:27 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.025s
2025-08-01 22:52:27 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:52:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.107s
2025-08-01 22:52:29 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.107s
2025-08-01 22:52:29 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:52:43 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 22:52:43 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 22:52:43 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 22:52:43 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 22:52:43 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 22:52:43 - uvicorn.error - INFO - server - _serve:94 - Finished server process [20648]
2025-08-01 22:52:45 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:52:45 - uvicorn.error - INFO - server - _serve:84 - Started server process [16272]
2025-08-01 22:52:45 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 22:52:45 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 22:52:45 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 22:52:45 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 22:52:45 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 22:52:45 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 22:53:04 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.140s
2025-08-01 22:53:06 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.140s
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.042s
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.008s
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.022s
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - app.app.core.cache - INFO - cache - clear:69 - Cache cleared
2025-08-01 22:53:06 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.001s
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 400 Bad Request"
2025-08-01 22:53:06 - app.app.core.cache - INFO - cache - clear:69 - Cache cleared
2025-08-01 22:53:06 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.002s
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.010s
2025-08-01 22:53:06 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 22:53:06 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - ERROR - base - _close_connection:376 - Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x000002090F159300>>
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\pool\base.py", line 372, in _close_connection
    self._dialect.do_terminate(connection)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 1133, in do_terminate
    dbapi_connection.terminate()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 907, in terminate
    self.await_(asyncio.shield(self._connection.close(timeout=2)))
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg\\protocol\\protocol.pyx", line 627, in close
  File "asyncpg\\protocol\\protocol.pyx", line 660, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1673, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 436, in create_task
    self._check_closed()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 515, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-08-01 22:53:07 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.027s
2025-08-01 22:53:07 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.027s
2025-08-01 22:53:07 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:53:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.121s
2025-08-01 22:53:08 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.121s
2025-08-01 22:53:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 22:53:30 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:53:30 - passlib.handlers.bcrypt - WARNING - bcrypt - _load_backend_mixin:622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 22:54:05 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 22:55:01 - app.api - INFO - logging - log_api_request:220 - GET /
2025-08-01 22:55:01 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:64371 - "GET / HTTP/1.1" 200
2025-08-01 22:55:15 - app.api - INFO - logging - log_api_request:220 - GET /docs
2025-08-01 22:55:15 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:64372 - "GET /docs HTTP/1.1" 404
2025-08-01 22:55:29 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/docs - 0.001s
2025-08-01 22:55:29 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:64377 - "GET /api/v1/docs HTTP/1.1" 404
2025-08-01 23:28:17 - app.api - INFO - logging - log_api_request:220 - GET / - 0.003s
2025-08-01 23:28:17 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:49367 - "GET / HTTP/1.1" 200
2025-08-01 23:28:29 - app.api - INFO - logging - log_api_request:220 - GET /docs - 0.002s
2025-08-01 23:28:29 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:49368 - "GET /docs HTTP/1.1" 404
2025-08-01 23:32:50 - app.api - INFO - logging - log_api_request:220 - GET / - 0.001s
2025-08-01 23:32:50 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:50226 - "GET / HTTP/1.1" 200
2025-08-01 23:33:04 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.060s
2025-08-01 23:33:04 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.060s
2025-08-01 23:33:04 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:50227 - "GET /api/v1/monitoring/health HTTP/1.1" 200
2025-08-01 23:34:49 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-01 23:34:49 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:50442 - "GET /api/v1/monitoring/health/simple HTTP/1.1" 200
2025-08-01 23:35:19 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.010s
2025-08-01 23:35:19 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:50443 - "GET /api/v1/monitoring/performance HTTP/1.1" 200
2025-08-01 23:35:56 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.006s
2025-08-01 23:35:56 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:50513 - "GET /api/v1/monitoring/metrics HTTP/1.1" 200
2025-08-01 23:36:19 - app.api - INFO - logging - log_api_request:220 - GET / - 0.001s
2025-08-01 23:36:19 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:50514 - "GET / HTTP/1.1" 200
2025-08-01 23:36:21 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.023s
2025-08-01 23:36:21 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:50514 - "GET /api/v1/monitoring/info HTTP/1.1" 200
2025-08-01 23:37:23 - app.api - INFO - logging - log_api_request:220 - GET / - 0.001s
2025-08-01 23:37:23 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:50582 - "GET / HTTP/1.1" 200
2025-08-01 23:37:24 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.001s
2025-08-01 23:37:24 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:50582 - "GET /api/v1/monitoring/cache/stats HTTP/1.1" 200
2025-08-01 23:38:47 - app.api - INFO - logging - log_api_request:220 - GET /docs
2025-08-01 23:38:47 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:50672 - "GET /docs HTTP/1.1" 404
2025-08-01 23:39:27 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:39:27 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:39:27 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:39:27 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 23:39:27 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:39:27 - uvicorn.error - INFO - server - _serve:94 - Finished server process [16272]
2025-08-01 23:40:03 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:40:03 - uvicorn.error - INFO - server - _serve:84 - Started server process [5564]
2025-08-01 23:40:03 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:40:03 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:40:03 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 23:40:03 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 23:40:03 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:40:03 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:40:13 - app.api - INFO - logging - log_api_request:220 - GET /docs - 0.001s
2025-08-01 23:40:13 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:50747 - "GET /docs HTTP/1.1" 404
2025-08-01 23:42:09 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:42:10 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:42:10 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:42:10 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 23:42:10 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:42:10 - uvicorn.error - INFO - server - _serve:94 - Finished server process [5564]
2025-08-01 23:42:12 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:42:12 - uvicorn.error - INFO - server - _serve:84 - Started server process [15644]
2025-08-01 23:42:12 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:42:12 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:42:12 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 23:42:12 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 23:42:12 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:42:12 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:42:13 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:42:13 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:42:13 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:42:13 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 23:42:13 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:42:13 - uvicorn.error - INFO - server - _serve:94 - Finished server process [15644]
2025-08-01 23:42:14 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:42:14 - uvicorn.error - INFO - server - _serve:84 - Started server process [18784]
2025-08-01 23:42:14 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:42:14 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:42:15 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 23:42:15 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 23:42:15 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:42:15 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:47:25 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:47:26 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:47:26 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:47:26 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 23:47:26 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:47:26 - uvicorn.error - INFO - server - _serve:94 - Finished server process [18784]
2025-08-01 23:47:27 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:47:27 - uvicorn.error - INFO - server - _serve:84 - Started server process [17520]
2025-08-01 23:47:27 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:47:27 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:47:27 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 23:47:27 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 23:47:27 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:47:27 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:48:09 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:48:09 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:48:09 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:48:09 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 23:48:09 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:48:09 - uvicorn.error - INFO - server - _serve:94 - Finished server process [17520]
2025-08-01 23:48:11 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:48:11 - uvicorn.error - INFO - server - _serve:84 - Started server process [3000]
2025-08-01 23:48:11 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:48:11 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:48:11 - app.app.core.cache - INFO - cache - warm_cache:200 - Starting cache warm-up
2025-08-01 23:48:11 - app.app.core.cache - INFO - cache - warm_cache:214 - Cache warm-up completed
2025-08-01 23:48:11 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:48:11 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:48:22 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:48:22 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:48:22 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:48:22 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 23:48:22 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:48:22 - uvicorn.error - INFO - server - _serve:94 - Finished server process [3000]
2025-08-01 23:48:24 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:48:24 - uvicorn.error - INFO - server - _serve:84 - Started server process [11548]
2025-08-01 23:48:24 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:48:24 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:48:24 - app.app.core.cache - INFO - cache - warm_cache:203 - Starting cache warm-up
2025-08-01 23:48:24 - app.app.core.cache - INFO - cache - warm_cache:217 - Cache warm-up completed
2025-08-01 23:48:24 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:48:24 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:48:57 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:48:57 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:48:57 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:48:57 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 23:48:57 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:48:57 - uvicorn.error - INFO - server - _serve:94 - Finished server process [11548]
2025-08-01 23:49:40 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:49:40 - uvicorn.error - INFO - server - _serve:84 - Started server process [13452]
2025-08-01 23:49:40 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:49:40 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:49:40 - app.app.core.cache - INFO - cache - warm_cache:285 - Starting cache warm-up
2025-08-01 23:49:40 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:49:40 - app.app.core.cache - INFO - cache - warm_cache:299 - Cache warm-up completed
2025-08-01 23:49:40 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:49:40 - app.app.core.cache - ERROR - cache - cleanup_cache_task:341 - Cache cleanup task error: 'RedisCache' object has no attribute 'cleanup_expired'
2025-08-01 23:49:40 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:50:11 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:50:11 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:50:11 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:50:11 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 23:50:11 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:50:11 - uvicorn.error - INFO - server - _serve:94 - Finished server process [13452]
2025-08-01 23:50:12 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:50:13 - uvicorn.error - INFO - server - _serve:84 - Started server process [18220]
2025-08-01 23:50:13 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:50:13 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:50:13 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-01 23:50:13 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:50:13 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-01 23:50:13 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:50:13 - app.app.core.cache - ERROR - cache - cleanup_cache_task:336 - Cache cleanup task error: 'RedisCache' object has no attribute 'cleanup_expired'
2025-08-01 23:50:13 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:50:32 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:50:32 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:50:32 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:50:32 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 23:50:32 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:50:32 - uvicorn.error - INFO - server - _serve:94 - Finished server process [18220]
2025-08-01 23:50:51 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:50:51 - uvicorn.error - INFO - server - _serve:84 - Started server process [19612]
2025-08-01 23:50:51 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:50:51 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:50:51 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-01 23:50:51 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:50:51 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-01 23:50:51 - uvicorn.error - ERROR - on - send:121 - Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\contextlib.py", line 199, in __aenter__
    return await anext(self.gen)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 134, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\contextlib.py", line 199, in __aenter__
    return await anext(self.gen)
  File "D:\nextjs\webapp\backend\app\main.py", line 37, in lifespan
    asyncio.create_task(cleanup_cache_task())
NameError: name 'cleanup_cache_task' is not defined

2025-08-01 23:50:51 - uvicorn.error - ERROR - on - startup:59 - Application startup failed. Exiting.
2025-08-01 23:51:04 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:51:04 - uvicorn.error - INFO - server - _serve:84 - Started server process [20196]
2025-08-01 23:51:04 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:51:04 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:51:04 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-01 23:51:04 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:51:04 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-01 23:51:04 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:51:04 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:51:22 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:51:22 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:51:22 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:51:22 - app.main - INFO - main - lifespan:46 - FastAPI application shut down successfully
2025-08-01 23:51:22 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:51:22 - uvicorn.error - INFO - server - _serve:94 - Finished server process [20196]
2025-08-01 23:51:23 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:51:23 - uvicorn.error - INFO - server - _serve:84 - Started server process [12796]
2025-08-01 23:51:23 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:51:23 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:51:23 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-01 23:51:23 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:51:23 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-01 23:51:23 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:51:23 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:51:39 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:51:39 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:51:39 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:51:39 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-01 23:51:39 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-01 23:51:39 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:51:39 - uvicorn.error - INFO - server - _serve:94 - Finished server process [12796]
2025-08-01 23:51:41 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:51:41 - uvicorn.error - INFO - server - _serve:84 - Started server process [13644]
2025-08-01 23:51:41 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:51:41 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:51:41 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-01 23:51:41 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:51:41 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-01 23:51:41 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:51:41 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:53:26 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:53:26 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:53:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.013s
2025-08-01 23:53:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 23:53:50 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:53:50 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:53:50 - app.app.core.cache - INFO - cache - clear_all:137 - All cache entries cleared
2025-08-01 23:53:50 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 23:53:50 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.017s
2025-08-01 23:53:50 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 23:53:50 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-01 23:53:50 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-01 23:53:50 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:53:50 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:53:50 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:53:50 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.004s
2025-08-01 23:53:50 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 23:54:14 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:54:14 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:54:14 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:54:14 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-01 23:54:14 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-01 23:54:14 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:54:14 - uvicorn.error - INFO - server - _serve:94 - Finished server process [13644]
2025-08-01 23:54:15 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:54:15 - uvicorn.error - INFO - server - _serve:84 - Started server process [5916]
2025-08-01 23:54:15 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:54:15 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:54:15 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-01 23:54:16 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:54:16 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-01 23:54:16 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:54:16 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:54:32 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:54:32 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:54:32 - app.app.core.cache - INFO - cache - clear_all:137 - All cache entries cleared
2025-08-01 23:54:32 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 23:54:32 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.015s
2025-08-01 23:54:32 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 23:54:32 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-01 23:54:32 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-01 23:54:32 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:54:32 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:54:32 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:54:32 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.006s
2025-08-01 23:54:32 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 23:54:46 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:54:46 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:54:46 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.015s
2025-08-01 23:54:46 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 23:55:15 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:55:15 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:55:15 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:55:15 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-01 23:55:15 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-01 23:55:15 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:55:15 - uvicorn.error - INFO - server - _serve:94 - Finished server process [5916]
2025-08-01 23:55:17 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:55:17 - uvicorn.error - INFO - server - _serve:84 - Started server process [17744]
2025-08-01 23:55:17 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:55:17 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:55:17 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-01 23:55:17 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:55:17 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-01 23:55:17 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:55:17 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:55:30 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:55:30 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:55:30 - app.app.core.cache - INFO - cache - clear_all:137 - All cache entries cleared
2025-08-01 23:55:30 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 23:55:30 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.015s
2025-08-01 23:55:30 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 23:55:30 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-01 23:55:30 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-01 23:55:30 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:55:30 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:55:30 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:30 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.004s
2025-08-01 23:55:30 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 23:55:41 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:55:41 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:55:42 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.140s
2025-08-01 23:55:42 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.140s
2025-08-01 23:55:42 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 23:55:42 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 23:55:42 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 23:55:42 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.007s
2025-08-01 23:55:42 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 23:55:42 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-01 23:55:42 - app.app.api.v1.routes.monitoring - ERROR - monitoring - get_metrics:217 - Metrics endpoint failed: 'total_entries'
2025-08-01 23:55:42 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/monitoring/metrics: Failed to retrieve metrics
2025-08-01 23:55:42 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.008s
2025-08-01 23:55:42 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 500 Internal Server Error"
2025-08-01 23:55:43 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.015s
2025-08-01 23:55:43 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 200 OK"
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-01 23:55:43 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 23:55:43 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-01 23:55:43 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:43 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.004s
2025-08-01 23:55:43 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 23:55:43 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 23:55:43 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 23:55:43 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 23:55:43 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 23:55:43 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 400 Bad Request"
2025-08-01 23:55:43 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: await wasn't used with future
2025-08-01 23:55:43 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:275 - Cache cleared via monitoring endpoint
2025-08-01 23:55:43 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-01 23:55:43 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 23:55:43 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:43 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:43 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.005s
2025-08-01 23:55:43 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 23:55:43 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.002s
2025-08-01 23:55:43 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 23:55:43 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.006s
2025-08-01 23:55:43 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 23:55:43 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - ERROR - base - _close_connection:376 - Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x0000012ABEA42890>>
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\pool\base.py", line 372, in _close_connection
    self._dialect.do_terminate(connection)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 1133, in do_terminate
    dbapi_connection.terminate()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 907, in terminate
    self.await_(asyncio.shield(self._connection.close(timeout=2)))
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg\\protocol\\protocol.pyx", line 627, in close
  File "asyncpg\\protocol\\protocol.pyx", line 660, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1673, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 436, in create_task
    self._check_closed()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 515, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-08-01 23:55:43 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:44 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.030s
2025-08-01 23:55:44 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.030s
2025-08-01 23:55:44 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 23:55:44 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:44 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:55:44 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:44 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:55:44 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:44 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:55:44 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:55:44 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:45 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.133s
2025-08-01 23:55:45 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.133s
2025-08-01 23:55:45 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 23:55:56 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:55:56 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:55:56 - app.app.api.v1.routes.monitoring - ERROR - monitoring - get_metrics:217 - Metrics endpoint failed: 'total_entries'
2025-08-01 23:55:56 - app.core.exceptions - WARNING - exceptions - http_exception_handler:164 - HTTP exception on http://testserver/api/v1/monitoring/metrics: Failed to retrieve metrics
2025-08-01 23:55:56 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.018s
2025-08-01 23:55:56 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 500 Internal Server Error"
2025-08-01 23:56:36 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:56:37 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:56:37 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:56:37 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-01 23:56:37 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-01 23:56:37 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:56:37 - uvicorn.error - INFO - server - _serve:94 - Finished server process [17744]
2025-08-01 23:56:38 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:56:38 - uvicorn.error - INFO - server - _serve:84 - Started server process [19532]
2025-08-01 23:56:38 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:56:38 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:56:38 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-01 23:56:38 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:56:38 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-01 23:56:38 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:56:38 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:58:07 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-01 23:58:07 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-01 23:58:07 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-01 23:58:07 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-01 23:58:07 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-01 23:58:07 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-01 23:58:07 - uvicorn.error - INFO - server - _serve:94 - Finished server process [19532]
2025-08-01 23:58:09 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:58:09 - uvicorn.error - INFO - server - _serve:84 - Started server process [21432]
2025-08-01 23:58:09 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-01 23:58:09 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-01 23:58:09 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-01 23:58:09 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:58:09 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-01 23:58:09 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-01 23:58:09 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-01 23:58:26 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:58:26 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:58:26 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.019s
2025-08-01 23:58:26 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 200 OK"
2025-08-01 23:59:54 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-01 23:59:54 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-01 23:59:55 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.130s
2025-08-01 23:59:55 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.130s
2025-08-01 23:59:55 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 23:59:55 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 23:59:55 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 23:59:55 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.006s
2025-08-01 23:59:55 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 23:59:55 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-01 23:59:55 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.006s
2025-08-01 23:59:55 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 200 OK"
2025-08-01 23:59:56 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.020s
2025-08-01 23:59:56 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 200 OK"
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-01 23:59:56 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:301 - Cache cleared via monitoring endpoint
2025-08-01 23:59:56 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-01 23:59:56 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:59:56 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.052s
2025-08-01 23:59:56 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 23:59:56 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 23:59:56 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 23:59:56 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 23:59:56 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 23:59:56 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 400 Bad Request"
2025-08-01 23:59:56 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: await wasn't used with future
2025-08-01 23:59:56 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:301 - Cache cleared via monitoring endpoint
2025-08-01 23:59:56 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.002s
2025-08-01 23:59:56 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-01 23:59:56 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:59:56 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:59:56 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.006s
2025-08-01 23:59:56 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-01 23:59:56 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-01 23:59:56 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-01 23:59:56 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.007s
2025-08-01 23:59:56 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-01 23:59:56 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - ERROR - base - _close_connection:376 - Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x000001870DD426B0>>
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\pool\base.py", line 372, in _close_connection
    self._dialect.do_terminate(connection)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 1133, in do_terminate
    dbapi_connection.terminate()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 907, in terminate
    self.await_(asyncio.shield(self._connection.close(timeout=2)))
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg\\protocol\\protocol.pyx", line 627, in close
  File "asyncpg\\protocol\\protocol.pyx", line 660, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1673, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 436, in create_task
    self._check_closed()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 515, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-08-01 23:59:56 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:59:57 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.023s
2025-08-01 23:59:57 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.023s
2025-08-01 23:59:57 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-01 23:59:57 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:57 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:59:57 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:57 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:59:57 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:57 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:59:57 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-01 23:59:57 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:59:58 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.124s
2025-08-01 23:59:58 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.124s
2025-08-01 23:59:58 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-02 00:00:20 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:00:20 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-02 00:00:20 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-02 00:00:20 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-02 00:00:20 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-02 00:00:20 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-02 00:00:20 - uvicorn.error - INFO - server - _serve:94 - Finished server process [21432]
2025-08-02 00:00:22 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:00:22 - uvicorn.error - INFO - server - _serve:84 - Started server process [19228]
2025-08-02 00:00:22 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:00:22 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:00:22 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:00:22 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:00:22 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:00:22 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:00:22 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:02:13 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:02:13 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.119s
2025-08-02 00:02:14 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.119s
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.009s
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.006s
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.020s
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-02 00:02:14 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:301 - Cache cleared via monitoring endpoint
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.005s
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 400 Bad Request"
2025-08-02 00:02:14 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: await wasn't used with future
2025-08-02 00:02:14 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:301 - Cache cleared via monitoring endpoint
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.001s
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:14 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.004s
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/performance - 0.010s
2025-08-02 00:02:14 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/performance "HTTP/1.1 200 OK"
2025-08-02 00:02:14 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - ERROR - base - _close_connection:376 - Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x000001CF9EBC2890>>
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\pool\base.py", line 372, in _close_connection
    self._dialect.do_terminate(connection)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 1133, in do_terminate
    dbapi_connection.terminate()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\dialects\postgresql\asyncpg.py", line 907, in terminate
    self.await_(asyncio.shield(self._connection.close(timeout=2)))
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1504, in close
    await self._protocol.close(timeout)
  File "asyncpg\\protocol\\protocol.pyx", line 627, in close
  File "asyncpg\\protocol\\protocol.pyx", line 660, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\asyncpg\connection.py", line 1673, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 436, in create_task
    self._check_closed()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 515, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-08-02 00:02:14 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:16 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.074s
2025-08-02 00:02:16 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.074s
2025-08-02 00:02:16 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-02 00:02:16 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:16 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-02 00:02:16 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:16 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-02 00:02:16 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:16 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-02 00:02:16 - asyncio - WARNING - proactor_events - write:352 - socket.send() raised exception.
2025-08-02 00:02:16 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:17 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health - 1.126s
2025-08-02 00:02:17 - app.performance - WARNING - performance - dispatch:47 - Slow request: GET /api/v1/monitoring/health - 1.126s
2025-08-02 00:02:17 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health "HTTP/1.1 200 OK"
2025-08-02 00:02:36 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:02:36 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:02:36 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.017s
2025-08-02 00:02:36 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-02 00:02:36 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: Event loop is closed
2025-08-02 00:02:36 - app.app.api.v1.routes.monitoring - INFO - monitoring - clear_cache:301 - Cache cleared via monitoring endpoint
2025-08-02 00:02:36 - app.api - INFO - logging - log_api_request:220 - POST /api/v1/monitoring/cache/clear - 0.003s
2025-08-02 00:02:36 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: POST http://testserver/api/v1/monitoring/cache/clear "HTTP/1.1 200 OK"
2025-08-02 00:02:36 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-02 00:02:36 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.004s
2025-08-02 00:02:36 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 200 OK"
2025-08-02 00:02:36 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key test:integration: await wasn't used with future
2025-08-02 00:02:36 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key test:integration: await wasn't used with future
2025-08-02 00:04:08 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:04:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple - 0.001s
2025-08-02 00:04:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/health/simple "HTTP/1.1 200 OK"
2025-08-02 00:04:08 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:04:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/cache/stats - 0.015s
2025-08-02 00:04:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/cache/stats "HTTP/1.1 200 OK"
2025-08-02 00:04:08 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-02 00:04:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/metrics - 0.006s
2025-08-02 00:04:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/metrics "HTTP/1.1 200 OK"
2025-08-02 00:04:08 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/info - 0.016s
2025-08-02 00:04:08 - httpx - INFO - _client - _send_single_request:1025 - HTTP Request: GET http://testserver/api/v1/monitoring/info "HTTP/1.1 200 OK"
2025-08-02 00:06:58 - app.api - INFO - logging - log_api_request:220 - GET /docs
2025-08-02 00:06:58 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:53831 - "GET /docs HTTP/1.1" 404
2025-08-02 00:08:56 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:08:56 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-02 00:08:56 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-02 00:08:56 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-02 00:08:56 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-02 00:08:56 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-02 00:08:56 - uvicorn.error - INFO - server - _serve:94 - Finished server process [19228]
2025-08-02 00:08:58 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:08:58 - uvicorn.error - INFO - server - _serve:84 - Started server process [20052]
2025-08-02 00:08:58 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:08:58 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00094s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.007109s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.008719s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.009967s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:08:58 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:08:58 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:08:58 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:08:58 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:08:58 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:08:58 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:09:10 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:09:10 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-02 00:09:10 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-02 00:09:10 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-02 00:09:10 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-02 00:09:10 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-02 00:09:10 - uvicorn.error - INFO - server - _serve:94 - Finished server process [20052]
2025-08-02 00:10:16 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:10:16 - uvicorn.error - INFO - server - _serve:84 - Started server process [12264]
2025-08-02 00:10:16 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:10:16 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00074s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.006491s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.008074s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.009367s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:10:16 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:10:16 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:10:16 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:10:16 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:10:16 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:10:16 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:10:45 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:11:21 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:11:21 - uvicorn.error - INFO - server - _serve:84 - Started server process [2844]
2025-08-02 00:11:21 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:11:21 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00065s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.00913s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01245s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.0142s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:11:21 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:11:21 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:11:21 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:11:21 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:11:21 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:11:21 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:12:05 - app.api - INFO - logging - log_api_request:220 - GET /docs
2025-08-02 00:12:05 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:54305 - "GET /docs HTTP/1.1" 200
2025-08-02 00:12:54 - app.api - INFO - logging - log_api_request:220 - GET /docs - 0.001s
2025-08-02 00:12:54 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:54387 - "GET /docs HTTP/1.1" 200
2025-08-02 00:12:56 - app.api - INFO - logging - log_api_request:220 - GET /redoc - 0.001s
2025-08-02 00:12:56 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:54393 - "GET /redoc HTTP/1.1" 200
2025-08-02 00:12:58 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/openapi.json - 0.037s
2025-08-02 00:12:58 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:54397 - "GET /api/v1/openapi.json HTTP/1.1" 200
2025-08-02 00:13:00 - app.api - INFO - logging - log_api_request:220 - GET /
2025-08-02 00:13:00 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:54404 - "GET / HTTP/1.1" 200
2025-08-02 00:13:02 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/monitoring/health/simple
2025-08-02 00:13:02 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:54408 - "GET /api/v1/monitoring/health/simple HTTP/1.1" 200
2025-08-02 00:14:54 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:14:54 - uvicorn.error - INFO - server - _serve:84 - Started server process [6200]
2025-08-02 00:14:54 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:14:54 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00046s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.009547s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.0111s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01291s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:14:54 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:14:54 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:14:54 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:14:54 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:14:54 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:14:54 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:15:05 - app.api - INFO - logging - log_api_request:220 - GET /docs - 0.002s
2025-08-02 00:15:05 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:54578 - "GET /docs HTTP/1.1" 200
2025-08-02 00:15:05 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/openapi.json - 0.027s
2025-08-02 00:15:05 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:54578 - "GET /api/v1/openapi.json HTTP/1.1" 200
2025-08-02 00:18:07 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:18:07 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-02 00:18:07 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-02 00:18:07 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-02 00:18:07 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-02 00:18:07 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-02 00:18:07 - uvicorn.error - INFO - server - _serve:94 - Finished server process [6200]
2025-08-02 00:18:09 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:18:09 - uvicorn.error - INFO - server - _serve:84 - Started server process [3948]
2025-08-02 00:18:09 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:18:09 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00218s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.008681s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01007s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01127s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:18:09 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:18:09 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:18:09 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:18:09 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:18:09 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:18:09 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:18:45 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:18:45 - uvicorn.error - INFO - server - _serve:94 - Finished server process [3948]
2025-08-02 00:18:45 - uvicorn.error - ERROR - on - send:134 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\uvicorn\server.py", line 70, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\uvicorn\server.py", line 331, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 701, in lifespan
    await receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-08-02 00:21:30 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:21:58 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:21:58 - uvicorn.error - INFO - server - _serve:84 - Started server process [2232]
2025-08-02 00:21:58 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:21:58 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00053s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.007231s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01155s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01343s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:21:59 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:21:59 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:21:59 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:21:59 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:21:59 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:21:59 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:22:36 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/openapi.json - 0.023s
2025-08-02 00:22:36 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:55157 - "GET /api/v1/openapi.json HTTP/1.1" 200
2025-08-02 00:24:03 - app.api - INFO - logging - log_api_request:220 - GET /api/v1/openapi.json - 0.002s
2025-08-02 00:24:03 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:55255 - "GET /api/v1/openapi.json HTTP/1.1" 200
2025-08-02 00:25:29 - app - INFO - logging - setup_logging:136 - Logging configured for environment: development
2025-08-02 00:25:29 - uvicorn.error - INFO - server - _serve:84 - Started server process [11648]
2025-08-02 00:25:29 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:25:29 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00051s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01025s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.0127s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01456s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:25:29 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:25:29 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:25:29 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:25:29 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:25:29 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:25:29 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:29:06 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:29:06 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-02 00:29:06 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-02 00:29:06 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-02 00:29:06 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-02 00:29:06 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-02 00:29:06 - uvicorn.error - INFO - server - _serve:94 - Finished server process [11648]
2025-08-02 00:29:08 - app - INFO - logging - setup_logging:143 - Logging configured for environment: development
2025-08-02 00:29:08 - uvicorn.error - INFO - server - _serve:84 - Started server process [14356]
2025-08-02 00:29:08 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:29:08 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00084s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.008215s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01022s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01197s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:08 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:29:08 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:29:08 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:29:08 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:29:08 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:29:08 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:29:25 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:29:26 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-02 00:29:26 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-02 00:29:26 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-02 00:29:26 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-02 00:29:26 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-02 00:29:26 - uvicorn.error - INFO - server - _serve:94 - Finished server process [14356]
2025-08-02 00:29:27 - app - INFO - logging - setup_logging:149 - Logging configured for environment: development
2025-08-02 00:29:27 - uvicorn.error - INFO - server - _serve:84 - Started server process [10120]
2025-08-02 00:29:27 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:29:27 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00043s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.005138s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.006221s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.007154s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:27 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:29:27 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:29:27 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:29:27 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:29:27 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:29:27 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:29:44 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:29:44 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-02 00:29:44 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-02 00:29:44 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-02 00:29:44 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-02 00:29:44 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-02 00:29:44 - uvicorn.error - INFO - server - _serve:94 - Finished server process [10120]
2025-08-02 00:29:46 - app - INFO - logging - setup_logging:154 - Logging configured for environment: development
2025-08-02 00:29:46 - uvicorn.error - INFO - server - _serve:84 - Started server process [20616]
2025-08-02 00:29:46 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:29:46 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00045s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.005113s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.006271s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.00751s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:46 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:29:46 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:29:46 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:29:46 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:29:46 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:29:46 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:29:57 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:29:57 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-02 00:29:57 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-02 00:29:57 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-02 00:29:57 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-02 00:29:57 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-02 00:29:57 - uvicorn.error - INFO - server - _serve:94 - Finished server process [20616]
2025-08-02 00:29:58 - app - INFO - logging - setup_logging:154 - Logging configured for environment: development
2025-08-02 00:29:58 - uvicorn.error - INFO - server - _serve:84 - Started server process [21040]
2025-08-02 00:29:58 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:29:58 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00048s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.005638s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.006572s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.007466s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:29:58 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:29:58 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:29:58 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:29:58 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:29:58 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:29:58 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:30:18 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:30:18 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-02 00:30:18 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-02 00:30:18 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-02 00:30:18 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-02 00:30:18 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-02 00:30:18 - uvicorn.error - INFO - server - _serve:94 - Finished server process [21040]
2025-08-02 00:30:20 - app - INFO - logging - setup_logging:154 - Logging configured for environment: development
2025-08-02 00:30:20 - uvicorn.error - INFO - server - _serve:84 - Started server process [12672]
2025-08-02 00:30:20 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:30:20 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00035s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.004695s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.005659s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.006531s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:30:20 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:30:20 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:30:20 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:30:20 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:30:20 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:30:20 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:30:33 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:30:33 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-02 00:30:33 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-02 00:30:33 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-02 00:30:33 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-02 00:30:33 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-02 00:30:33 - uvicorn.error - INFO - server - _serve:94 - Finished server process [12672]
2025-08-02 00:30:35 - app - INFO - logging - setup_logging:154 - Logging configured for environment: development
2025-08-02 00:30:35 - uvicorn.error - INFO - server - _serve:84 - Started server process [9700]
2025-08-02 00:30:35 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:30:35 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00034s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.005708s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.006807s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.007806s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:30:35 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:30:35 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:30:35 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:30:35 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:30:35 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:30:35 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:30:49 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:30:49 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-02 00:30:49 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-02 00:30:49 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-02 00:30:49 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-02 00:30:49 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-02 00:30:49 - uvicorn.error - INFO - server - _serve:94 - Finished server process [9700]
2025-08-02 00:30:50 - app - INFO - logging - setup_logging:154 - Logging configured for environment: development
2025-08-02 00:30:50 - uvicorn.error - INFO - server - _serve:84 - Started server process [4332]
2025-08-02 00:30:50 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:30:50 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00042s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.005378s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.006322s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.007223s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:30:51 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:30:51 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:30:51 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:30:51 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:30:51 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:30:51 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:31:34 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-02 00:31:34 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-02 00:31:34 - app.main - INFO - main - lifespan:44 - Shutting down FastAPI application...
2025-08-02 00:31:34 - app.app.core.cache - INFO - cache - close:188 - Redis connection closed
2025-08-02 00:31:34 - app.main - INFO - main - lifespan:51 - FastAPI application shut down successfully
2025-08-02 00:31:34 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-02 00:31:34 - uvicorn.error - INFO - server - _serve:94 - Finished server process [4332]
2025-08-02 00:31:36 - app - INFO - logging - setup_logging:154 - Logging configured for environment: development
2025-08-02 00:31:36 - app.test - INFO - <string> - <module>:15 - This is an INFO message
2025-08-02 00:31:36 - app.test - WARNING - <string> - <module>:16 - This is a WARNING message
2025-08-02 00:31:36 - app.test - ERROR - <string> - <module>:17 - This is an ERROR message
2025-08-02 00:32:44 - app - INFO - logging - setup_logging:154 - Logging configured for environment: development
2025-08-02 00:32:44 - uvicorn.error - INFO - server - _serve:84 - Started server process [18548]
2025-08-02 00:32:44 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:32:44 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00040s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.005071s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.006612s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.007656s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:32:45 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:32:45 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:32:45 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:32:45 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:32:45 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:32:45 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:33:14 - app.api - INFO - logging - log_api_request:238 - GET /
2025-08-02 00:33:14 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:56017 - "GET / HTTP/1.1" 200
2025-08-02 00:34:52 - app - INFO - logging - setup_logging:154 - Logging configured for environment: development
2025-08-02 00:34:52 - app.test - INFO - test_logging - test_logging_levels:41 - ℹ️  INFO: This is an info message
2025-08-02 00:34:52 - app.test - WARNING - test_logging - test_logging_levels:42 - ⚠️  WARNING: This is a warning message
2025-08-02 00:34:52 - app.test - ERROR - test_logging - test_logging_levels:43 - ❌ ERROR: This is an error message
2025-08-02 00:35:05 - app - INFO - logging - setup_logging:154 - Logging configured for environment: development
2025-08-02 00:35:05 - app.test - INFO - test_logging - test_logging_levels:41 - ℹ️  INFO: This is an info message
2025-08-02 00:35:05 - app.test - WARNING - test_logging - test_logging_levels:42 - ⚠️  WARNING: This is a warning message
2025-08-02 00:35:05 - app.test - ERROR - test_logging - test_logging_levels:43 - ❌ ERROR: This is an error message
2025-08-02 00:35:24 - app - INFO - logging - setup_logging:154 - Logging configured for environment: development
2025-08-02 00:35:24 - uvicorn.error - INFO - server - _serve:84 - Started server process [1948]
2025-08-02 00:35:24 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 00:35:24 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00036s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.005331s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.006263s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.007113s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 00:35:24 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 00:35:24 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 00:35:24 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 00:35:24 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 00:35:24 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 00:35:24 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-02 00:35:44 - app.api - INFO - logging - log_api_request:238 - GET /
2025-08-02 00:35:44 - uvicorn.access - INFO - httptools_impl - send:476 - 127.0.0.1:56255 - "GET / HTTP/1.1" 200
2025-08-02 12:41:11 - app - INFO - logging - setup_logging:154 - Logging configured for environment: development
2025-08-02 12:41:11 - uvicorn.error - INFO - server - _serve:84 - Started server process [11072]
2025-08-02 12:41:11 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-02 12:41:11 - app.main - INFO - main - lifespan:25 - Starting up FastAPI application...
2025-08-02 12:41:11 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select pg_catalog.version()
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - select current_schema()
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - show standard_conforming_strings
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [raw sql] ()
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _begin_impl:1094 - BEGIN (implicit)
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [generated in 0.00041s] ('users', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01403s ago] ('user_sessions', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01515s ago] ('email_verifications', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1893 - SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = $1::VARCHAR AND pg_catalog.pg_class.relkind = ANY (ARRAY[$2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR]) AND pg_catalog.pg_table_is_visible(pg_catalog.pg_class.oid) AND pg_catalog.pg_namespace.nspname != $7::VARCHAR
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _exec_single_context:1898 - [cached since 0.01619s ago] ('blogs', 'r', 'p', 'f', 'v', 'm', 'pg_catalog')
2025-08-02 12:41:12 - sqlalchemy.engine.Engine - INFO - base - _commit_impl:1137 - COMMIT
2025-08-02 12:41:12 - app.app.core.cache - INFO - cache - warm_cache:280 - Starting cache warm-up
2025-08-02 12:41:12 - app.app.core.cache - INFO - cache - _get_redis:41 - Redis connection established successfully
2025-08-02 12:41:12 - app.app.core.cache - INFO - cache - warm_cache:294 - Cache warm-up completed
2025-08-02 12:41:12 - app.main - INFO - main - lifespan:39 - FastAPI application started successfully
2025-08-02 12:41:12 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
