import { screen, waitFor } from '@testing-library/react'
import { LoginForm } from '../login-form'
import { renderWithProviders, fillForm, submitForm, mockFetch } from '@/lib/__tests__/test-utils'

// Mock the auth actions
jest.mock('@/lib/actions/auth-actions', () => ({
  loginAction: jest.fn(),
}))

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}))

describe('LoginForm', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Reset localStorage mock
    localStorage.clear()
  })

  it('renders login form with all required fields', () => {
    renderWithProviders(<LoginForm />)

    expect(screen.getByRole('heading', { name: /welcome back/i })).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
    expect(screen.getByText(/don't have an account/i)).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /sign up/i })).toBeInTheDocument()
  })

  it('displays validation errors for empty fields', async () => {
    renderWithProviders(<LoginForm />)

    await submitForm(screen.getByRole, /sign in/i)

    await waitFor(() => {
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      
      expect(emailInput).toBeRequired()
      expect(passwordInput).toBeRequired()
    })
  })

  it('submits form with valid data', async () => {
    const mockLoginAction = require('@/lib/actions/auth-actions').loginAction
    mockLoginAction.mockResolvedValue({
      success: true,
      message: 'Login successful',
      tokens: {
        access_token: 'mock-token',
        refresh_token: 'mock-refresh',
        token_type: 'bearer',
      },
    })

    renderWithProviders(<LoginForm />)

    await fillForm(screen.getByLabelText, {
      email: '<EMAIL>',
      password: 'password123',
    })

    await submitForm(screen.getByRole, /sign in/i)

    await waitFor(() => {
      expect(mockLoginAction).toHaveBeenCalled()
    })
  })

  it('displays error message on login failure', async () => {
    const mockLoginAction = require('@/lib/actions/auth-actions').loginAction
    mockLoginAction.mockResolvedValue({
      success: false,
      message: 'Invalid credentials',
      errors: {},
    })

    renderWithProviders(<LoginForm />)

    await fillForm(screen.getByLabelText, {
      email: '<EMAIL>',
      password: 'wrongpassword',
    })

    await submitForm(screen.getByRole, /sign in/i)

    await waitFor(() => {
      expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument()
    })
  })

  it('displays field-specific validation errors', async () => {
    const mockLoginAction = require('@/lib/actions/auth-actions').loginAction
    mockLoginAction.mockResolvedValue({
      success: false,
      message: '',
      errors: {
        email: ['Please enter a valid email address'],
        password: ['Password is required'],
      },
    })

    renderWithProviders(<LoginForm />)

    await fillForm(screen.getByLabelText, {
      email: 'invalid-email',
      password: '',
    })

    await submitForm(screen.getByRole, /sign in/i)

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument()
      expect(screen.getByText(/password is required/i)).toBeInTheDocument()
    })
  })

  it('disables form during submission', async () => {
    const mockLoginAction = require('@/lib/actions/auth-actions').loginAction
    // Mock a delayed response
    mockLoginAction.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))

    renderWithProviders(<LoginForm />)

    await fillForm(screen.getByLabelText, {
      email: '<EMAIL>',
      password: 'password123',
    })

    const submitButton = screen.getByRole('button', { name: /sign in/i })
    const emailInput = screen.getByLabelText(/email/i)
    const passwordInput = screen.getByLabelText(/password/i)

    await submitForm(screen.getByRole, /sign in/i)

    // Check that form is disabled during submission
    expect(submitButton).toBeDisabled()
    expect(emailInput).toBeDisabled()
    expect(passwordInput).toBeDisabled()
    expect(screen.getByText(/signing in/i)).toBeInTheDocument()
  })

  it('has forgot password link', () => {
    renderWithProviders(<LoginForm />)

    const forgotPasswordLink = screen.getByRole('link', { name: /forgot password/i })
    expect(forgotPasswordLink).toBeInTheDocument()
    expect(forgotPasswordLink).toHaveAttribute('href', '/forgot-password')
  })

  it('has terms and privacy links', () => {
    renderWithProviders(<LoginForm />)

    expect(screen.getByRole('link', { name: /terms of service/i })).toHaveAttribute('href', '/terms')
    expect(screen.getByRole('link', { name: /privacy policy/i })).toHaveAttribute('href', '/privacy')
  })
})
