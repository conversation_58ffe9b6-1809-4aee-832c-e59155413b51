import { loginAction, signupAction, logoutAction } from '../auth-actions'
import { config } from '@/lib/config'

// Mock fetch
global.fetch = jest.fn()

// Mock cookies
jest.mock('next/headers', () => ({
  cookies: jest.fn(() => ({
    set: jest.fn(),
    delete: jest.fn(),
  })),
}))

describe('Auth Actions', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(fetch as jest.Mock).mockClear()
  })

  describe('loginAction', () => {
    it('handles successful login', async () => {
      const mockTokens = {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        token_type: 'bearer',
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue(mockTokens),
      })

      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'password123')

      const result = await loginAction(
        { success: false, message: '', errors: {} },
        formData
      )

      expect(result.success).toBe(true)
      expect(result.message).toBe('Login successful')
      expect(result.tokens).toEqual(mockTokens)
      expect(fetch).toHaveBeenCalledWith(
        `${config.API_BASE_URL}/${config.API_VERSION}/auth/login`,
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123',
          }),
        })
      )
    })

    it('handles login failure with error message', async () => {
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: jest.fn().mockResolvedValue({
          detail: 'Invalid credentials',
        }),
      })

      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'wrongpassword')

      const result = await loginAction(
        { success: false, message: '', errors: {} },
        formData
      )

      expect(result.success).toBe(false)
      expect(result.message).toBe('Invalid credentials')
    })

    it('handles validation errors', async () => {
      const formData = new FormData()
      formData.append('email', 'invalid-email')
      formData.append('password', '')

      const result = await loginAction(
        { success: false, message: '', errors: {} },
        formData
      )

      expect(result.success).toBe(false)
      expect(result.errors).toBeDefined()
    })

    it('handles network errors', async () => {
      ;(fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))

      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'password123')

      const result = await loginAction(
        { success: false, message: '', errors: {} },
        formData
      )

      expect(result.success).toBe(false)
      expect(result.message).toBe('Network error occurred. Please try again.')
    })
  })

  describe('signupAction', () => {
    it('handles successful signup', async () => {
      const mockResponse = {
        message: 'User created successfully',
        user: {
          id: '1',
          email: '<EMAIL>',
          full_name: 'Test User',
        },
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: jest.fn().mockResolvedValue(mockResponse),
      })

      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'password123')
      formData.append('confirmPassword', 'password123')
      formData.append('full_name', 'Test User')

      const result = await signupAction(
        { success: false, message: '', errors: {} },
        formData
      )

      expect(result.success).toBe(true)
      expect(result.message).toBe('User created successfully')
      expect(fetch).toHaveBeenCalledWith(
        `${config.API_BASE_URL}/${config.API_VERSION}/auth/register`,
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123',
            full_name: 'Test User',
          }),
        })
      )
    })

    it('handles signup failure', async () => {
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: jest.fn().mockResolvedValue({
          detail: 'Email already exists',
        }),
      })

      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'password123')
      formData.append('confirmPassword', 'password123')

      const result = await signupAction(
        { success: false, message: '', errors: {} },
        formData
      )

      expect(result.success).toBe(false)
      expect(result.message).toBe('Email already exists')
    })

    it('validates password confirmation', async () => {
      const formData = new FormData()
      formData.append('email', '<EMAIL>')
      formData.append('password', 'password123')
      formData.append('confirmPassword', 'different-password')

      const result = await signupAction(
        { success: false, message: '', errors: {} },
        formData
      )

      expect(result.success).toBe(false)
      expect(result.errors?.confirmPassword).toContain('Passwords do not match')
    })
  })

  describe('logoutAction', () => {
    it('handles successful logout', async () => {
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue({ message: 'Logged out successfully' }),
      })

      const result = await logoutAction()

      expect(result.success).toBe(true)
      expect(result.message).toBe('Logged out successfully')
    })

    it('handles logout errors gracefully', async () => {
      ;(fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))

      const result = await logoutAction()

      // Should still succeed locally even if server request fails
      expect(result.success).toBe(true)
    })
  })
})
