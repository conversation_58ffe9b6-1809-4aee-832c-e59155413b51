import { loginSchema, signupSchema } from '../validations'

describe('Validation Schemas', () => {
  describe('loginSchema', () => {
    it('validates correct login data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'Password123',
      }

      const result = loginSchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual(validData)
      }
    })

    it('rejects invalid email format', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'Password123',
      }

      const result = loginSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('email')
        expect(result.error.issues[0].message).toMatch(/please enter a valid email address/i)
      }
    })

    it('rejects empty email', () => {
      const invalidData = {
        email: '',
        password: 'Password123',
      }

      const result = loginSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('email')
      }
    })

    it('rejects empty password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: '',
      }

      const result = loginSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('password')
      }
    })

    it('rejects missing fields', () => {
      const invalidData = {}

      const result = loginSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues).toHaveLength(2) // email and password required
      }
    })


  })

  describe('signupSchema', () => {
    it('validates correct signup data with full name', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'Password123',
        full_name: 'John Doe',
      }

      const result = signupSchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data).toEqual(validData)
      }
    })

    it('validates correct signup data without full name', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'Password123',
      }

      const result = signupSchema.safeParse(validData)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.email).toBe(validData.email)
        expect(result.data.password).toBe(validData.password)
        expect(result.data.confirmPassword).toBe(validData.confirmPassword)
      }
    })

    it('rejects password shorter than 8 characters', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'Pass1',
        confirmPassword: 'Pass1',
      }

      const result = signupSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        const passwordError = result.error.issues.find(issue => issue.path.includes('password'))
        expect(passwordError?.message).toMatch(/at least 8 characters/i)
      }
    })

    it('rejects mismatched passwords', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'DifferentPass123',
      }

      const result = signupSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        const confirmError = result.error.issues.find(issue =>
          issue.path.includes('confirmPassword') || issue.message.includes('match')
        )
        expect(confirmError?.message).toMatch(/passwords.*match/i)
      }
    })

    it('rejects invalid email format', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'Password123',
        confirmPassword: 'Password123',
      }

      const result = signupSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('email')
        expect(result.error.issues[0].message).toMatch(/please enter a valid email address/i)
      }
    })

    it('accepts empty full name', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'Password123',
        confirmPassword: 'Password123',
        full_name: '',
      }

      const result = signupSchema.safeParse(validData)
      expect(result.success).toBe(true)
    })



    it('rejects missing required fields', () => {
      const invalidData = {
        email: '<EMAIL>',
        // missing password and confirmPassword
      }

      const result = signupSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.length).toBeGreaterThanOrEqual(2)
      }
    })
  })
})
